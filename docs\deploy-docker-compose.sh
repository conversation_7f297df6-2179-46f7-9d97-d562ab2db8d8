#!/bin/bash

# DL引擎 Docker Compose 自动化部署脚本
# 版本: 1.0.0
# 作者: DL Engine Team

set -e

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
COMPOSE_FILE="$SCRIPT_DIR/docker-compose.complete.yml"
ENV_FILE="$PROJECT_ROOT/.env"
ENV_TEMPLATE="$SCRIPT_DIR/.env.template"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
DL引擎 Docker Compose 部署脚本

用法: $0 [选项] [命令]

命令:
  deploy      部署所有服务 (默认)
  start       启动所有服务
  stop        停止所有服务
  restart     重启所有服务
  status      查看服务状态
  logs        查看服务日志
  cleanup     清理所有资源
  update      更新服务镜像
  backup      备份数据
  restore     恢复数据
  health      健康检查
  scale       扩缩容服务

选项:
  -h, --help              显示帮助信息
  -v, --verbose           详细输出
  -f, --file FILE         指定compose文件
  -e, --env-file FILE     指定环境变量文件
  --no-build              不重新构建镜像
  --pull                  拉取最新镜像
  --force                 强制执行操作

示例:
  $0 deploy               # 部署所有服务
  $0 start                # 启动所有服务
  $0 logs api-gateway     # 查看API网关日志
  $0 scale collaboration-service=3  # 扩展协作服务到3个实例
  $0 backup               # 备份数据
  $0 cleanup --force      # 强制清理所有资源

EOF
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查Docker服务状态
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请启动 Docker 服务"
        exit 1
    fi
    
    log_info "依赖检查完成"
}

# 检查环境变量文件
check_env_file() {
    log_info "检查环境变量文件..."
    
    if [ ! -f "$ENV_FILE" ]; then
        log_warn "环境变量文件不存在: $ENV_FILE"
        
        if [ -f "$ENV_TEMPLATE" ]; then
            log_info "复制环境变量模板..."
            cp "$ENV_TEMPLATE" "$ENV_FILE"
            log_warn "请编辑 $ENV_FILE 文件并填入正确的配置值"
            log_warn "必需配置项: MYSQL_ROOT_PASSWORD, REDIS_PASSWORD, JWT_SECRET, OPENAI_API_KEY"
            
            read -p "是否现在编辑环境变量文件? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                ${EDITOR:-nano} "$ENV_FILE"
            else
                log_error "请先配置环境变量文件后再运行部署"
                exit 1
            fi
        else
            log_error "环境变量模板文件不存在: $ENV_TEMPLATE"
            exit 1
        fi
    fi
    
    # 验证必需的环境变量
    source "$ENV_FILE"
    
    local required_vars=("MYSQL_ROOT_PASSWORD" "REDIS_PASSWORD" "JWT_SECRET")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "缺少必需的环境变量: ${missing_vars[*]}"
        log_error "请编辑 $ENV_FILE 文件并设置这些变量"
        exit 1
    fi
    
    log_info "环境变量检查完成"
}

# 检查系统资源
check_system_resources() {
    log_info "检查系统资源..."
    
    # 检查可用内存
    local available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    local required_memory=8192  # 8GB
    
    if [ "$available_memory" -lt "$required_memory" ]; then
        log_warn "可用内存不足: ${available_memory}MB (推荐: ${required_memory}MB)"
        log_warn "系统可能运行缓慢，建议增加内存或调整服务配置"
    fi
    
    # 检查磁盘空间
    local available_disk=$(df -m "$PROJECT_ROOT" | awk 'NR==2{print $4}')
    local required_disk=20480  # 20GB
    
    if [ "$available_disk" -lt "$required_disk" ]; then
        log_warn "可用磁盘空间不足: ${available_disk}MB (推荐: ${required_disk}MB)"
        log_warn "可能影响数据存储和日志记录"
    fi
    
    log_info "系统资源检查完成"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    local dirs=(
        "$PROJECT_ROOT/uploads/knowledge"
        "$PROJECT_ROOT/uploads/voice"
        "$PROJECT_ROOT/uploads/avatars"
        "$PROJECT_ROOT/temp/knowledge"
        "$PROJECT_ROOT/temp/voice"
        "$PROJECT_ROOT/logs"
        "$PROJECT_ROOT/models/recommendation"
        "$PROJECT_ROOT/models/ai"
        "$PROJECT_ROOT/config/mysql"
        "$PROJECT_ROOT/config/redis"
        "$PROJECT_ROOT/nginx/conf.d"
        "$PROJECT_ROOT/ssl"
    )
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_debug "创建目录: $dir"
        fi
    done
    
    log_info "目录创建完成"
}

# 部署服务
deploy_services() {
    log_info "开始部署DL引擎服务..."
    
    # 检查依赖
    check_dependencies
    check_env_file
    check_system_resources
    create_directories
    
    # 构建和启动服务
    log_info "构建和启动基础设施服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d mysql redis elasticsearch minio
    
    # 等待基础设施就绪
    log_info "等待基础设施服务启动..."
    sleep 30
    
    # 启动核心微服务
    log_info "启动核心微服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d service-registry
    sleep 15
    
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d \
        user-service project-service asset-service render-service
    sleep 20
    
    # 启动AI服务
    log_info "启动AI智能服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d \
        knowledge-base-service rag-dialogue-service avatar-service voice-service \
        recommendation-service ai-model-service
    sleep 15
    
    # 启动协作服务
    log_info "启动协作服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d \
        collaboration-service-1 collaboration-service-2 collaboration-load-balancer
    sleep 10
    
    # 启动区块链和边缘服务
    log_info "启动区块链和边缘服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d \
        blockchain-service edge-game-server signaling-service
    sleep 10
    
    # 启动API网关和前端
    log_info "启动API网关和前端..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d \
        api-gateway editor
    sleep 10
    
    # 启动监控和代理服务
    log_info "启动监控和代理服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d \
        monitoring-service nginx
    
    log_info "所有服务部署完成"
    
    # 显示访问信息
    show_access_info
}

# 启动服务
start_services() {
    log_info "启动所有服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" start
    log_info "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止所有服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" stop
    log_info "服务停止完成"
}

# 重启服务
restart_services() {
    log_info "重启所有服务..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" restart
    log_info "服务重启完成"
}

# 查看服务状态
show_status() {
    log_info "服务状态:"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps
}

# 查看服务日志
show_logs() {
    local service="$1"
    if [ -n "$service" ]; then
        log_info "查看 $service 服务日志:"
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f "$service"
    else
        log_info "查看所有服务日志:"
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" logs -f
    fi
}

# 清理资源
cleanup_resources() {
    local force="$1"
    
    if [ "$force" != "--force" ]; then
        log_warn "此操作将删除所有容器、网络和数据卷"
        read -p "确定要继续吗? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作已取消"
            return 0
        fi
    fi
    
    log_info "清理所有资源..."
    
    # 停止并删除容器
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" down -v --remove-orphans
    
    # 删除未使用的镜像
    docker image prune -f
    
    # 删除未使用的网络
    docker network prune -f
    
    # 删除未使用的数据卷
    docker volume prune -f
    
    log_info "资源清理完成"
}

# 更新服务镜像
update_services() {
    log_info "更新服务镜像..."
    
    # 拉取最新镜像
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" pull
    
    # 重新构建自定义镜像
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" build --no-cache
    
    # 重启服务
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d
    
    log_info "服务更新完成"
}

# 备份数据
backup_data() {
    log_info "备份数据..."
    
    local backup_dir="$PROJECT_ROOT/backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"
    
    # 备份MySQL数据
    log_info "备份MySQL数据..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T mysql \
        mysqldump -u root -p"$MYSQL_ROOT_PASSWORD" --all-databases > "$backup_dir/mysql_backup.sql"
    
    # 备份Redis数据
    log_info "备份Redis数据..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec redis redis-cli --rdb /data/dump.rdb
    docker cp "$(docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps -q redis):/data/dump.rdb" "$backup_dir/redis_backup.rdb"
    
    # 备份上传文件
    log_info "备份上传文件..."
    tar -czf "$backup_dir/uploads_backup.tar.gz" -C "$PROJECT_ROOT" uploads/
    
    log_info "数据备份完成: $backup_dir"
}

# 恢复数据
restore_data() {
    local backup_dir="$1"
    
    if [ -z "$backup_dir" ] || [ ! -d "$backup_dir" ]; then
        log_error "请指定有效的备份目录"
        exit 1
    fi
    
    log_info "从 $backup_dir 恢复数据..."
    
    # 恢复MySQL数据
    if [ -f "$backup_dir/mysql_backup.sql" ]; then
        log_info "恢复MySQL数据..."
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec -T mysql \
            mysql -u root -p"$MYSQL_ROOT_PASSWORD" < "$backup_dir/mysql_backup.sql"
    fi
    
    # 恢复Redis数据
    if [ -f "$backup_dir/redis_backup.rdb" ]; then
        log_info "恢复Redis数据..."
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" stop redis
        docker cp "$backup_dir/redis_backup.rdb" "$(docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" ps -q redis):/data/dump.rdb"
        docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" start redis
    fi
    
    # 恢复上传文件
    if [ -f "$backup_dir/uploads_backup.tar.gz" ]; then
        log_info "恢复上传文件..."
        tar -xzf "$backup_dir/uploads_backup.tar.gz" -C "$PROJECT_ROOT"
    fi
    
    log_info "数据恢复完成"
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    local services=(
        "mysql:3306"
        "redis:6379"
        "elasticsearch:9200"
        "api-gateway:3000"
        "user-service:4001"
        "project-service:4002"
        "asset-service:4003"
        "render-service:4004"
        "knowledge-base-service:4011"
        "rag-dialogue-service:4012"
        "avatar-service:4013"
        "voice-service:4014"
        "editor:80"
    )
    
    local failed_services=()
    
    for service_port in "${services[@]}"; do
        local service="${service_port%:*}"
        local port="${service_port#*:}"
        
        if docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" exec "$service" \
           curl -f "http://localhost:$port/health" &> /dev/null; then
            log_info "✓ $service 健康"
        else
            log_error "✗ $service 不健康"
            failed_services+=("$service")
        fi
    done
    
    if [ ${#failed_services[@]} -eq 0 ]; then
        log_info "所有服务健康检查通过"
    else
        log_error "以下服务健康检查失败: ${failed_services[*]}"
        exit 1
    fi
}

# 扩缩容服务
scale_service() {
    local service_scale="$1"
    
    if [ -z "$service_scale" ]; then
        log_error "请指定服务和副本数，格式: service=replicas"
        exit 1
    fi
    
    log_info "扩缩容服务: $service_scale"
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d --scale "$service_scale"
    log_info "扩缩容完成"
}

# 显示访问信息
show_access_info() {
    log_info "部署完成！访问信息："
    
    cat << EOF

================================
DL引擎服务访问信息
================================

前端应用:
  编辑器界面:     http://localhost
  HTTPS访问:      https://localhost

API服务:
  API网关:        http://localhost:3000
  API文档:        http://localhost:3000/api/docs
  用户服务:       http://localhost:4001
  项目服务:       http://localhost:4002
  资产服务:       http://localhost:4003
  渲染服务:       http://localhost:4004

AI智能服务:
  知识库服务:     http://localhost:4011
  RAG对话服务:    http://localhost:4012
  数字人服务:     http://localhost:4013
  语音服务:       http://localhost:4014

区块链和边缘:
  区块链服务:     http://localhost:3006
  边缘游戏服务器: http://localhost:8080
  信令服务:       http://localhost:3020

基础设施:
  MySQL:          localhost:3306
  Redis:          localhost:6379
  Elasticsearch:  http://localhost:9200
  MinIO控制台:    http://localhost:9001

监控服务:
  监控服务:       http://localhost:3100

常用命令:
  查看服务状态:   $0 status
  查看日志:       $0 logs [service-name]
  健康检查:       $0 health
  停止服务:       $0 stop
  重启服务:       $0 restart

================================

EOF
}

# 主函数
main() {
    local command="${1:-deploy}"
    local arg2="$2"
    local arg3="$3"
    
    case "$command" in
        "deploy")
            deploy_services
            ;;
        "start")
            start_services
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$arg2"
            ;;
        "cleanup")
            cleanup_resources "$arg2"
            ;;
        "update")
            update_services
            ;;
        "backup")
            backup_data
            ;;
        "restore")
            restore_data "$arg2"
            ;;
        "health")
            health_check
            ;;
        "scale")
            scale_service "$arg2"
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知命令: $command"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
