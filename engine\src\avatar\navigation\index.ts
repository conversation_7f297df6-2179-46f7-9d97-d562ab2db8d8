/**
 * 数字人导航集成模块
 * 
 * 该模块将数字人系统与导航系统深度集成，提供完整的数字人路径跟随功能。
 * 支持智能动画映射、路径事件处理、多数字人协调等高级功能。
 * 
 * 主要功能：
 * - 数字人路径跟随
 * - 智能动画映射
 * - 路径事件处理
 * - 多数字人协调
 * - 性能优化
 * - 调试和监控
 * 
 * 使用示例：
 * ```typescript
 * import { AvatarPathSystem, AvatarPathFollowingComponent, PathAnimationMapper } from '../avatar/navigation';
 * 
 * // 创建数字人路径系统
 * const avatarPathSystem = new AvatarPathSystem(world, {
 *   debug: true,
 *   enableAutoPathAssignment: true,
 *   maxConcurrentFollowers: 20
 * });
 * 
 * // 添加系统到世界
 * world.addSystem(avatarPathSystem);
 * 
 * // 为数字人分配路径
 * const success = avatarPathSystem.assignPathToAvatar(avatarEntityId, path, {
 *   autoStart: true,
 *   speedMultiplier: 1.2,
 *   loop: true
 * });
 * 
 * // 监听路径事件
 * avatarPathSystem.on('avatarPathCompleted', (data) => {
 *   console.log('数字人完成路径', data);
 * });
 * ```
 */

// 导入依赖的类型和类
import { AvatarPathFollowingComponent } from '../components/AvatarPathFollowingComponent';
import { AvatarPathSystem } from '../systems/AvatarPathSystem';
import { PathAnimationMapper } from '../utils/PathAnimationMapper';

// 导出组件
export { AvatarPathFollowingComponent } from '../components/AvatarPathFollowingComponent';

// 导出系统
export { AvatarPathSystem } from '../systems/AvatarPathSystem';

// 导出工具类
export { PathAnimationMapper } from '../utils/PathAnimationMapper';

// 导出类型定义
export type {
  AvatarPathFollowingOptions
} from '../components/AvatarPathFollowingComponent';

export type {
  AvatarPathSystemOptions
} from '../systems/AvatarPathSystem';

export type {
  AnimationMappingRule,
  AnimationContext
} from '../utils/PathAnimationMapper';

// 重新导出导航模块的核心类型
export {
  PathEventType,
  LoopMode,
  InterpolationType
} from '../../navigation/types';

export type {
  PathEventData,
  PathFollowingOptions
} from '../../navigation/types';

export { PathFollowingState } from '../../navigation/components/PathFollowingComponent';

/**
 * 数字人路径集成工厂
 */
export class AvatarPathIntegrationFactory {
  /**
   * 创建完整的数字人路径系统
   * @param world 世界实例
   * @param options 配置选项
   * @returns 系统实例
   */
  public static createAvatarPathSystem(
    world: any,
    options: {
      debug?: boolean;
      enableAutoPathAssignment?: boolean;
      maxConcurrentFollowers?: number;
      animationPreset?: 'BASIC' | 'ADVANCED' | 'SOCIAL';
    } = {}
  ): AvatarPathSystem {
    const system = new AvatarPathSystem(world, {
      debug: options.debug || false,
      enableAutoPathAssignment: options.enableAutoPathAssignment || false,
      maxConcurrentFollowers: options.maxConcurrentFollowers || 50
    });

    // 如果指定了动画预设，应用到系统中
    if (options.animationPreset) {
      // 这里可以扩展系统以支持动画预设配置
      console.log(`应用动画预设: ${options.animationPreset}`);
    }

    return system;
  }

  /**
   * 为实体创建路径跟随组件
   * @param entity 实体
   * @param options 配置选项
   * @returns 组件实例
   */
  public static createPathFollowingComponent(
    entity: any,
    options: {
      path?: any;
      autoStart?: boolean;
      speedMultiplier?: number;
      loop?: boolean;
      animationPreset?: 'BASIC' | 'ADVANCED' | 'SOCIAL';
      enableSmartAnimation?: boolean;
    } = {}
  ): AvatarPathFollowingComponent {
    const component = new AvatarPathFollowingComponent(entity, {
      path: options.path,
      autoStart: options.autoStart || false,
      speedMultiplier: options.speedMultiplier || 1.0,
      loop: options.loop || false,
      enableAnimationTransition: options.enableSmartAnimation !== false,
      enableLookAtInterpolation: options.enableSmartAnimation !== false,
      enablePathEvents: true
    });

    return component;
  }

  /**
   * 创建动画映射器
   * @param preset 预设类型
   * @returns 映射器实例
   */
  public static createAnimationMapper(
    preset: 'BASIC' | 'ADVANCED' | 'SOCIAL' = 'ADVANCED'
  ): PathAnimationMapper {
    const mapper = new PathAnimationMapper();
    mapper.applyPreset(preset);
    return mapper;
  }

  /**
   * 创建数字人路径场景
   * @param world 世界实例
   * @param config 场景配置
   * @returns 场景配置结果
   */
  public static createAvatarPathScene(
    world: any,
    config: {
      avatars: Array<{
        entityId: string;
        pathData: any;
        options?: any;
      }>;
      systemOptions?: any;
      globalAnimationPreset?: 'BASIC' | 'ADVANCED' | 'SOCIAL';
    }
  ): {
    system: AvatarPathSystem;
    components: AvatarPathFollowingComponent[];
    mapper: PathAnimationMapper;
  } {
    // 创建系统
    const system = this.createAvatarPathSystem(world, config.systemOptions);

    // 创建动画映射器
    const mapper = this.createAnimationMapper(config.globalAnimationPreset);

    // 为每个数字人创建路径跟随组件
    const components: AvatarPathFollowingComponent[] = [];

    config.avatars.forEach(avatarConfig => {
      const entity = world.getEntity(avatarConfig.entityId);
      if (entity) {
        const component = this.createPathFollowingComponent(entity, {
          path: avatarConfig.pathData,
          ...avatarConfig.options
        });
        components.push(component);

        // 分配路径到系统
        system.assignPathToAvatar(
          avatarConfig.entityId,
          avatarConfig.pathData,
          avatarConfig.options
        );
      }
    });

    return {
      system,
      components,
      mapper
    };
  }
}

/**
 * 数字人路径工具函数
 */
export const AvatarPathUtils = {
  /**
   * 批量启动数字人路径
   * @param system 路径系统
   * @param entityIds 实体ID列表
   */
  batchStartPaths: (system: AvatarPathSystem, entityIds: string[]) => {
    system.batchOperation(entityIds, 'start');
  },

  /**
   * 批量停止数字人路径
   * @param system 路径系统
   * @param entityIds 实体ID列表
   */
  batchStopPaths: (system: AvatarPathSystem, entityIds: string[]) => {
    system.batchOperation(entityIds, 'stop');
  },

  /**
   * 获取所有数字人的路径状态
   * @param system 路径系统
   * @returns 状态列表
   */
  getAllPathStates: (system: AvatarPathSystem) => {
    return system.getAllAvatarPathStates();
  },

  /**
   * 获取正在跟随路径的数字人数量
   * @param system 路径系统
   * @returns 数量
   */
  getActiveFollowersCount: (system: AvatarPathSystem) => {
    const stats = system.getStatistics();
    return stats.followingAvatars;
  },

  /**
   * 检查数字人是否正在跟随路径
   * @param system 路径系统
   * @param entityId 实体ID
   * @returns 是否正在跟随
   */
  isFollowingPath: (system: AvatarPathSystem, entityId: string) => {
    const state = system.getAvatarPathState(entityId);
    return state && state.state === 'running';
  },

  /**
   * 获取数字人路径进度
   * @param system 路径系统
   * @param entityId 实体ID
   * @returns 进度 (0-1)
   */
  getPathProgress: (system: AvatarPathSystem, entityId: string) => {
    const state = system.getAvatarPathState(entityId);
    return state ? state.progress : 0;
  },

  /**
   * 创建简单的巡逻路径
   * @param center 中心点
   * @param radius 半径
   * @param pointCount 路径点数量
   * @returns 路径数据
   */
  createPatrolPath: (
    center: { x: number; y: number; z: number },
    radius: number,
    pointCount: number = 4
  ) => {
    const points = [];
    
    for (let i = 0; i < pointCount; i++) {
      const angle = (i / pointCount) * Math.PI * 2;
      const x = center.x + Math.cos(angle) * radius;
      const z = center.z + Math.sin(angle) * radius;
      
      points.push({
        id: `patrol_point_${i}`,
        position: { x, y: center.y, z },
        waitTime: 1.0,
        speed: 1.5,
        animation: 'walk'
      });
    }

    return {
      id: `patrol_path_${Date.now()}`,
      name: '巡逻路径',
      points,
      loopMode: 'loop',
      interpolation: 'smooth'
    };
  },

  /**
   * 创建往返路径
   * @param startPoint 起点
   * @param endPoint 终点
   * @returns 路径数据
   */
  createBackAndForthPath: (
    startPoint: { x: number; y: number; z: number },
    endPoint: { x: number; y: number; z: number }
  ) => {
    return {
      id: `back_forth_path_${Date.now()}`,
      name: '往返路径',
      points: [
        {
          id: 'start_point',
          position: startPoint,
          waitTime: 2.0,
          speed: 1.0,
          animation: 'idle'
        },
        {
          id: 'end_point',
          position: endPoint,
          waitTime: 2.0,
          speed: 1.0,
          animation: 'idle'
        }
      ],
      loopMode: 'pingpong',
      interpolation: 'linear'
    };
  }
};

/**
 * 数字人路径常量
 */
export const AvatarPathConstants = {
  // 默认动画名称
  DEFAULT_ANIMATIONS: {
    IDLE: 'idle',
    WALK: 'walk',
    RUN: 'run',
    JUMP: 'jump',
    TALK: 'talk',
    WAVE: 'wave',
    BOW: 'bow'
  },

  // 默认速度值
  DEFAULT_SPEEDS: {
    IDLE: 0,
    WALK: 1.5,
    RUN: 4.0,
    SPRINT: 8.0
  },

  // 默认等待时间
  DEFAULT_WAIT_TIMES: {
    SHORT: 0.5,
    MEDIUM: 2.0,
    LONG: 5.0
  },

  // 路径类型
  PATH_TYPES: {
    LINEAR: 'linear',
    PATROL: 'patrol',
    RANDOM: 'random',
    SCRIPTED: 'scripted'
  }
};
