/**
 * AI模型接口
 * 定义AI模型的通用接口
 */
import { AIModelType } from '../AIModelType';
import { AIModelConfig } from '../AIModelConfig';

/**
 * 文本生成选项
 */
export interface TextGenerationOptions {
  /** 最大令牌数 */
  maxTokens?: number;
  
  /** 温度 (0-1) */
  temperature?: number;
  
  /** 是否使用流式响应 */
  stream?: boolean;
  
  /** 流式响应回调 */
  onStream?: (text: string) => void;
  
  /** 停止序列 */
  stopSequences?: string[];
  
  /** 采样方法 */
  samplingMethod?: 'greedy' | 'topk' | 'topp' | 'beam';
  
  /** Top-K采样参数 */
  topK?: number;
  
  /** Top-P采样参数 */
  topP?: number;
  
  /** Beam搜索宽度 */
  beamWidth?: number;
  
  /** 惩罚重复 */
  repetitionPenalty?: number;
  
  /** 长度惩罚 */
  lengthPenalty?: number;
  
  /** 自定义选项 */
  [key: string]: any;
}

/**
 * 图像生成选项
 */
export interface ImageGenerationOptions {
  /** 图像宽度 */
  width?: number;
  
  /** 图像高度 */
  height?: number;
  
  /** 生成步数 */
  steps?: number;
  
  /** 引导比例 */
  guidanceScale?: number;
  
  /** 种子 */
  seed?: number;
  
  /** 负面提示 */
  negativePrompt?: string;
  
  /** 采样器 */
  sampler?: string;
  
  /** 是否使用NSFW过滤 */
  safetyChecker?: boolean;
  
  /** 进度回调 */
  onProgress?: (progress: number) => void;
  
  /** 自定义选项 */
  [key: string]: any;
}

/**
 * 情感分析结果
 */
export interface EmotionAnalysisResult {
  /** 主要情感 */
  primaryEmotion: string;
  
  /** 情感强度 (0-1) */
  intensity: number;
  
  /** 情感分数映射 */
  scores: Record<string, number>;
  
  /** 置信度 (0-1) */
  confidence: number;
}

/**
 * 文本分类结果
 */
export interface TextClassificationResult {
  /** 分类标签 */
  label: string;
  
  /** 置信度 (0-1) */
  confidence: number;
  
  /** 所有标签及其置信度 */
  allLabels: Record<string, number>;
}

/**
 * 命名实体识别结果
 */
export interface NamedEntityRecognitionResult {
  /** 实体列表 */
  entities: Array<{
    /** 实体文本 */
    text: string;
    
    /** 实体类型 */
    type: string;
    
    /** 开始位置 */
    start: number;
    
    /** 结束位置 */
    end: number;
    
    /** 置信度 (0-1) */
    confidence: number;
  }>;
}

/**
 * 文本摘要结果
 */
export interface TextSummaryResult {
  /** 摘要文本 */
  summary: string;
  
  /** 摘要长度 */
  length: number;
  
  /** 压缩率 */
  compressionRate: number;
}

/**
 * 翻译结果
 */
export interface TranslationResult {
  /** 翻译文本 */
  translatedText: string;
  
  /** 源语言 */
  sourceLanguage: string;
  
  /** 目标语言 */
  targetLanguage: string;
  
  /** 置信度 (0-1) */
  confidence: number;
}

/**
 * AI模型接口
 */
export interface IAIModel {
  /** 获取模型ID */
  getId(): string;

  /** 获取模型类型 */
  getType(): AIModelType;

  /** 获取模型配置 */
  getConfig(): AIModelConfig;
  
  /** 初始化模型 */
  initialize(): Promise<boolean>;
  
  /** 生成文本 */
  generateText(prompt: string, options?: TextGenerationOptions): Promise<string>;
  
  /** 生成图像 */
  generateImage?(prompt: string, options?: ImageGenerationOptions): Promise<Blob>;
  
  /** 分析情感 */
  analyzeEmotion?(text: string): Promise<EmotionAnalysisResult>;
  
  /** 分类文本 */
  classifyText?(text: string, categories?: string[]): Promise<TextClassificationResult>;
  
  /** 命名实体识别 */
  recognizeEntities?(text: string): Promise<NamedEntityRecognitionResult>;
  
  /** 文本摘要 */
  summarizeText?(text: string, maxLength?: number): Promise<TextSummaryResult>;
  
  /** 翻译文本 */
  translateText?(text: string, targetLanguage: string, sourceLanguage?: string): Promise<TranslationResult>;
  
  /** 释放资源 */
  dispose(): void;
}
