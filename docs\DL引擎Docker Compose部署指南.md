# DL引擎 Docker Compose 部署指南

## 概述

DL（Digital Learning）引擎是一个基于微服务架构的多媒体游戏引擎平台，集成了3D渲染、AI智能推荐、区块链、边缘计算、RAG应用、语音服务和数字人等先进技术。本文档提供了使用Docker Compose进行完整部署的详细指南。

## 系统架构

### 核心组件

#### 基础设施层
- **MySQL 8.0**: 主数据存储，支持多数据库分离
- **Redis 7.0**: 缓存和会话存储，支持实时协作
- **Elasticsearch 8.11**: 向量数据库，支持RAG应用和智能搜索

#### 微服务层
- **服务注册中心** (端口: 3010/4010): 服务发现和注册
- **API网关** (端口: 3000): 统一入口，路由和认证
- **用户服务** (端口: 3001/4001): 用户管理和认证
- **项目服务** (端口: 3002/4002): 项目和场景管理
- **资产服务** (端口: 3003/4003): 资产文件管理
- **渲染服务** (端口: 3004/4004): 3D渲染和图像处理
- **协作服务** (端口: 3005-3007): 实时协作功能，支持负载均衡

#### AI与智能服务层
- **知识库服务** (端口: 4011): RAG知识库管理
- **RAG对话服务** (端口: 4012): 智能对话和问答
- **数字人服务** (端口: 4013): 数字人管理和交互
- **语音服务** (端口: 4014): 语音识别和合成
- **推荐服务** (端口: 3009): AI智能推荐系统
- **AI模型服务** (端口: 3008): AI模型管理和推理

#### 区块链与边缘计算层
- **区块链服务** (端口: 3006): NFT和数字资产管理
- **边缘游戏服务器** (端口: 8080/3030): 边缘计算节点
- **信令服务** (端口: 3020): WebRTC信令服务

#### 前端应用层
- **编辑器** (端口: 80): 基于React的可视化编辑器

#### 监控运维层
- **监控服务** (端口: 3100): 系统监控和告警
- **Prometheus** (端口: 9090): 指标收集
- **Grafana** (端口: 3000): 监控仪表板
- **ELK Stack**: 日志分析 (Elasticsearch:9200, Kibana:5601, Logstash:5044)

## 系统要求

### 硬件要求
- **CPU**: 8核心以上（推荐16核心）
- **内存**: 32GB以上（推荐64GB）
- **存储**: 500GB以上可用空间（推荐SSD）
- **网络**: 千兆网络，支持WebRTC传输

### 软件要求
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **操作系统**: Linux (Ubuntu 20.04+/CentOS 8+) 或 Windows 10/11 with WSL2

## 快速部署

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd newsystem

# 创建环境变量文件
cp .env.example .env

# 编辑环境变量
vim .env
```

### 2. 环境变量配置

创建 `.env` 文件并配置以下变量：

```bash
# 数据库配置
MYSQL_ROOT_PASSWORD=your_secure_mysql_password
DB_USERNAME=root
DB_PASSWORD=your_secure_mysql_password

# JWT配置
JWT_SECRET=your_jwt_secret_key_here

# Redis配置
REDIS_PASSWORD=your_redis_password

# AI服务配置
OPENAI_API_KEY=your_openai_api_key
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint
AZURE_OPENAI_API_KEY=your_azure_openai_api_key

# 语音服务配置
AZURE_SPEECH_KEY=your_azure_speech_key
AZURE_SPEECH_REGION=your_azure_speech_region
BAIDU_APP_ID=your_baidu_app_id
BAIDU_API_KEY=your_baidu_api_key
BAIDU_SECRET_KEY=your_baidu_secret_key

# 区块链配置
ETHEREUM_RPC_URL=your_ethereum_rpc_url
POLYGON_RPC_URL=your_polygon_rpc_url
PRIVATE_KEY=your_blockchain_private_key

# 边缘计算配置
EDGE_NODE_ID=edge-main-node
EDGE_REGION=main
CENTRAL_HUB_URL=http://localhost:3000
MAX_USERS_PER_EDGE=100
```

### 3. 一键部署

```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 4. 分步部署（推荐）

```bash
# 第一步：启动基础设施
docker-compose up -d mysql redis elasticsearch

# 等待基础设施启动完成
sleep 30

# 第二步：启动核心微服务
docker-compose up -d service-registry
sleep 15

docker-compose up -d user-service project-service asset-service render-service
sleep 20

# 第三步：启动AI和智能服务
docker-compose up -d knowledge-service rag-service avatar-service voice-service

# 第四步：启动协作服务
docker-compose up -d collaboration-service-1 collaboration-service-2 collaboration-load-balancer

# 第五步：启动API网关和前端
docker-compose up -d api-gateway editor

# 第六步：启动可选服务
docker-compose up -d blockchain-service edge-game-server signaling-service
```

## 服务配置详解

### 基础设施服务

#### MySQL配置
```yaml
mysql:
  image: mysql:8.0
  environment:
    MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
    MYSQL_ALLOW_EMPTY_PASSWORD: 'no'
    MYSQL_DATABASE: ir_engine
  volumes:
    - mysql_data:/var/lib/mysql
    - ./server/shared/init-scripts:/docker-entrypoint-initdb.d
  ports:
    - '3306:3306'
```

#### Redis配置
```yaml
redis:
  image: redis:7.0-alpine
  command: redis-server --requirepass ${REDIS_PASSWORD}
  volumes:
    - redis_data:/data
  ports:
    - '6379:6379'
```

#### Elasticsearch配置
```yaml
elasticsearch:
  image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
  environment:
    - discovery.type=single-node
    - xpack.security.enabled=false
    - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
  volumes:
    - elasticsearch_data:/usr/share/elasticsearch/data
  ports:
    - "9200:9200"
```

### 核心微服务配置

#### 服务注册中心
```yaml
service-registry:
  build:
    context: ./server/service-registry
    dockerfile: Dockerfile
  environment:
    - NODE_ENV=production
    - DB_HOST=mysql
    - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
    - SERVICE_REGISTRY_PORT=3010
  depends_on:
    mysql:
      condition: service_healthy
```

#### API网关
```yaml
api-gateway:
  build:
    context: ./server/api-gateway
    dockerfile: Dockerfile
  environment:
    - NODE_ENV=production
    - PORT=3000
    - JWT_SECRET=${JWT_SECRET}
    - SERVICE_REGISTRY_HOST=service-registry
  ports:
    - '3000:3000'
  depends_on:
    service-registry:
      condition: service_healthy
```

### AI智能服务配置

#### 知识库服务
```yaml
knowledge-service:
  build:
    context: ./server/knowledge-base-service
    dockerfile: Dockerfile
  environment:
    - NODE_ENV=production
    - PORT=4011
    - ELASTICSEARCH_URL=http://elasticsearch:9200
    - OPENAI_API_KEY=${OPENAI_API_KEY}
  volumes:
    - ./uploads/knowledge:/app/uploads
  ports:
    - "4011:4011"
```

#### 语音服务
```yaml
voice-service:
  build:
    context: ./server/voice-service
    dockerfile: Dockerfile
  environment:
    - NODE_ENV=production
    - PORT=4014
    - AZURE_SPEECH_KEY=${AZURE_SPEECH_KEY}
    - AZURE_SPEECH_REGION=${AZURE_SPEECH_REGION}
    - OPENAI_API_KEY=${OPENAI_API_KEY}
  volumes:
    - ./uploads/voice:/app/uploads
    - ./config/google-cloud-key.json:/app/config/google-cloud-key.json:ro
  ports:
    - "4014:4014"
```

## 监控部署

### 启动监控服务

```bash
# 启动监控堆栈
docker-compose -f docker-compose.monitoring.yml up -d

# 访问监控界面
# Grafana: http://localhost:3000 (admin/admin)
# Prometheus: http://localhost:9090
# Kibana: http://localhost:5601
```

### 监控配置

监控服务包括：
- **Prometheus**: 指标收集和存储
- **Grafana**: 可视化仪表板
- **Elasticsearch**: 日志存储
- **Kibana**: 日志分析
- **Logstash**: 日志处理
- **Filebeat**: 日志收集

## 验证部署

### 健康检查

```bash
# 检查所有服务状态
docker-compose ps

# 检查服务健康状态
curl http://localhost:3000/api/health
curl http://localhost:4010/health
curl http://localhost:4011/health
curl http://localhost:4012/health
curl http://localhost:4013/health
curl http://localhost:4014/health
```

### 功能测试

```bash
# 测试API网关
curl http://localhost:3000/api/docs

# 测试编辑器
curl http://localhost:80

# 测试RAG服务
curl -X POST http://localhost:4012/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Hello", "sessionId": "test"}'

# 测试语音服务
curl http://localhost:4014/api/voices
```

## 故障排除

### 常见问题

1. **服务启动失败**
```bash
# 查看服务日志
docker-compose logs service-name

# 重启服务
docker-compose restart service-name
```

2. **数据库连接问题**
```bash
# 检查MySQL状态
docker-compose exec mysql mysql -u root -p -e "SHOW DATABASES;"

# 重置数据库
docker-compose down
docker volume rm newsystem_mysql_data
docker-compose up -d mysql
```

3. **内存不足**
```bash
# 检查系统资源
docker stats

# 调整服务资源限制
# 编辑 docker-compose.yml 中的 deploy.resources 配置
```

4. **端口冲突**
```bash
# 检查端口占用
netstat -tulpn | grep :3000

# 修改端口映射
# 编辑 docker-compose.yml 中的 ports 配置
```

### 性能优化

1. **数据库优化**
```bash
# 调整MySQL配置
# 编辑 config/mysql/my.cnf
```

2. **缓存优化**
```bash
# 调整Redis配置
# 编辑 docker-compose.yml 中的Redis配置
```

3. **资源限制**
```yaml
# 在 docker-compose.yml 中添加资源限制
deploy:
  resources:
    limits:
      cpus: '2.0'
      memory: 4G
    reservations:
      cpus: '1.0'
      memory: 2G
```

## 备份与恢复

### 数据备份

```bash
# 备份MySQL数据
docker-compose exec mysql mysqldump -u root -p --all-databases > backup.sql

# 备份Redis数据
docker-compose exec redis redis-cli BGSAVE

# 备份Elasticsearch数据
curl -X PUT "localhost:9200/_snapshot/backup_repo" -H 'Content-Type: application/json' -d'
{
  "type": "fs",
  "settings": {
    "location": "/backup"
  }
}'
```

### 数据恢复

```bash
# 恢复MySQL数据
docker-compose exec -T mysql mysql -u root -p < backup.sql

# 恢复Redis数据
docker-compose exec redis redis-cli FLUSHALL
docker cp backup.rdb $(docker-compose ps -q redis):/data/dump.rdb
docker-compose restart redis
```

## 扩展部署

### 水平扩展

```bash
# 扩展协作服务
docker-compose up -d --scale collaboration-service=3

# 扩展渲染服务
docker-compose up -d --scale render-service=2
```

### 多环境部署

```bash
# 开发环境
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# 生产环境
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

## 安全配置

### SSL/TLS配置

```yaml
# 在 nginx 配置中添加SSL
server {
    listen 443 ssl;
    ssl_certificate /etc/ssl/certs/cert.pem;
    ssl_certificate_key /etc/ssl/private/key.pem;
}
```

### 网络安全

```yaml
# 创建自定义网络
networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
    internal: true
```

## 相关文档

- [Kubernetes部署指南](./DL引擎Kubernetes部署指南.md)
- [API文档](../api/README.md)
- [开发者指南](../developer/README.md)
- [用户手册](../user-manual/README.md)

## 技术支持

如需技术支持，请：
1. 查看本文档的故障排除部分
2. 检查项目的GitHub Issues
3. 联系技术支持团队
4. 参考官方文档和社区论坛
