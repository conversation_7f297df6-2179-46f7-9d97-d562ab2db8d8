# DL引擎快速开始指南

## 概述

本指南将帮助您快速部署和运行DL（Digital Learning）引擎。我们提供了Docker Compose和Kubernetes两种部署方式，推荐初学者使用Docker Compose进行快速体验。

## 系统要求

### 最低要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **CPU**: 4核心
- **内存**: 16GB
- **存储**: 100GB可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **操作系统**: Ubuntu 20.04+ / Windows 11 with WSL2
- **CPU**: 8核心以上
- **内存**: 32GB以上
- **存储**: 500GB SSD
- **网络**: 千兆网络

## 快速部署（Docker Compose）

### 步骤1: 安装依赖

#### Windows用户
```powershell
# 安装Docker Desktop
# 下载并安装: https://www.docker.com/products/docker-desktop

# 启用WSL2（推荐）
wsl --install
```

#### macOS用户
```bash
# 安装Docker Desktop
# 下载并安装: https://www.docker.com/products/docker-desktop

# 或使用Homebrew
brew install --cask docker
```

#### Linux用户
```bash
# 安装Docker
curl -fsSL https://get.docker.com | sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 重新登录以应用组权限
newgrp docker
```

### 步骤2: 获取项目代码

```bash
# 克隆项目（替换为实际的仓库地址）
git clone <repository-url>
cd newsystem

# 或下载ZIP包并解压
# wget <repository-zip-url>
# unzip newsystem.zip
# cd newsystem
```

### 步骤3: 配置环境变量

```bash
# 复制环境变量模板
cp docs/.env.template .env

# 编辑环境变量文件
# Windows: notepad .env
# macOS: open -e .env
# Linux: nano .env
```

**必需配置项**（最小配置）：
```bash
# 数据库密码
MYSQL_ROOT_PASSWORD=your_secure_password_123

# Redis密码
REDIS_PASSWORD=your_redis_password_123

# JWT密钥（至少32个字符）
JWT_SECRET=your_jwt_secret_key_minimum_32_characters_long

# OpenAI API密钥（可选，用于AI功能）
OPENAI_API_KEY=sk-your_openai_api_key_here
```

### 步骤4: 一键部署

#### Windows用户
```powershell
# 使用PowerShell脚本
.\docs\deploy-docker-compose.ps1 deploy

# 或直接使用Docker Compose
docker-compose -f docs/docker-compose.complete.yml up -d
```

#### Linux/macOS用户
```bash
# 使用部署脚本
chmod +x docs/deploy-docker-compose.sh
./docs/deploy-docker-compose.sh deploy

# 或直接使用Docker Compose
docker-compose -f docs/docker-compose.complete.yml up -d
```

### 步骤5: 验证部署

```bash
# 检查服务状态
docker-compose -f docs/docker-compose.complete.yml ps

# 查看日志
docker-compose -f docs/docker-compose.complete.yml logs -f

# 健康检查
curl http://localhost:3000/api/health
```

## 访问应用

部署完成后，您可以通过以下地址访问各个服务：

### 主要应用
- **编辑器界面**: http://localhost
- **API文档**: http://localhost:3000/api/docs

### AI服务
- **知识库服务**: http://localhost:4011
- **RAG对话服务**: http://localhost:4012
- **数字人服务**: http://localhost:4013
- **语音服务**: http://localhost:4014

### 管理界面
- **MinIO控制台**: http://localhost:9001 (admin/your_minio_password)
- **监控服务**: http://localhost:3100

## 常用操作

### 查看服务状态
```bash
# Docker Compose
docker-compose -f docs/docker-compose.complete.yml ps

# 使用脚本
./docs/deploy-docker-compose.sh status
```

### 查看日志
```bash
# 查看所有服务日志
docker-compose -f docs/docker-compose.complete.yml logs -f

# 查看特定服务日志
docker-compose -f docs/docker-compose.complete.yml logs -f api-gateway

# 使用脚本
./docs/deploy-docker-compose.sh logs api-gateway
```

### 重启服务
```bash
# 重启所有服务
docker-compose -f docs/docker-compose.complete.yml restart

# 重启特定服务
docker-compose -f docs/docker-compose.complete.yml restart api-gateway

# 使用脚本
./docs/deploy-docker-compose.sh restart
```

### 停止服务
```bash
# 停止所有服务
docker-compose -f docs/docker-compose.complete.yml stop

# 使用脚本
./docs/deploy-docker-compose.sh stop
```

### 清理资源
```bash
# 停止并删除所有容器和网络
docker-compose -f docs/docker-compose.complete.yml down

# 删除所有数据（谨慎使用）
docker-compose -f docs/docker-compose.complete.yml down -v

# 使用脚本
./docs/deploy-docker-compose.sh cleanup
```

## 功能测试

### 测试API网关
```bash
# 健康检查
curl http://localhost:3000/api/health

# 查看API文档
curl http://localhost:3000/api/docs
```

### 测试AI服务
```bash
# 测试RAG对话服务
curl -X POST http://localhost:4012/api/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "你好", "sessionId": "test-session"}'

# 测试语音服务
curl http://localhost:4014/api/voices
```

### 测试前端编辑器
```bash
# 访问编辑器
curl http://localhost

# 或在浏览器中打开
# http://localhost
```

## 故障排除

### 常见问题

#### 1. 端口冲突
```bash
# 检查端口占用
netstat -tulpn | grep :3000

# 修改端口映射
# 编辑 docs/docker-compose.complete.yml 中的 ports 配置
```

#### 2. 内存不足
```bash
# 检查系统内存
free -h

# 调整服务资源限制
# 编辑 docs/docker-compose.complete.yml 中的 deploy.resources 配置
```

#### 3. 磁盘空间不足
```bash
# 检查磁盘空间
df -h

# 清理Docker资源
docker system prune -a
```

#### 4. 服务启动失败
```bash
# 查看服务日志
docker-compose -f docs/docker-compose.complete.yml logs service-name

# 检查依赖服务状态
docker-compose -f docs/docker-compose.complete.yml ps
```

### 获取帮助

如果遇到问题，可以：

1. **查看日志**: 使用上述日志查看命令
2. **检查配置**: 确认环境变量配置正确
3. **重启服务**: 尝试重启相关服务
4. **查看文档**: 参考详细部署文档
5. **联系支持**: 提交Issue或联系技术支持

## 下一步

### 开发环境配置
如果您想进行开发，请参考：
- [开发者指南](../developer/README.md)
- [API文档](../api/README.md)

### 生产环境部署
如果您需要部署到生产环境，请参考：
- [Kubernetes部署指南](./DL引擎Kubernetes部署指南.md)
- [生产环境最佳实践](./部署总结文档.md)

### 功能配置
了解更多功能配置：
- [AI集成功能设计方案](./AI集成功能设计方案.md)
- [区块链集成技术实施指南](./DL引擎区块链集成技术实施指南.md)
- [边缘计算部署指南](./边缘计算部署指南.md)

## 支持和反馈

- **文档**: 查看完整文档
- **Issues**: 提交问题和建议
- **社区**: 加入开发者社区
- **支持**: 联系技术支持团队

---

**恭喜！** 您已经成功部署了DL引擎。现在可以开始探索这个强大的多媒体游戏引擎平台了！
