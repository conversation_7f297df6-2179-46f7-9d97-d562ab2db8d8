# DL引擎 Kubernetes 部署指南

## 概述

DL（Digital Learning）引擎是一个基于微服务架构的多媒体游戏引擎平台，集成了3D渲染、AI智能推荐、区块链、边缘计算、RAG应用、语音服务和数字人等先进技术。本文档提供了在Kubernetes集群上进行生产级部署的详细指南。

## 系统架构

### 集群架构设计

#### 命名空间划分
- **dl-engine-core**: 核心微服务
- **dl-engine-ai**: AI和智能服务
- **dl-engine-blockchain**: 区块链服务
- **dl-engine-edge**: 边缘计算节点
- **dl-engine-monitoring**: 监控和运维
- **dl-engine-storage**: 存储服务

#### 服务分层
```
┌─────────────────────────────────────────────────────────────┐
│                        Ingress Layer                        │
├─────────────────────────────────────────────────────────────┤
│                      Frontend Layer                         │
│  编辑器(80) │ 监控面板(3000) │ API文档(3000/docs)          │
├─────────────────────────────────────────────────────────────┤
│                      Gateway Layer                          │
│              API网关(3000) │ 负载均衡器                    │
├─────────────────────────────────────────────────────────────┤
│                    Microservices Layer                      │
│ 用户服务 │ 项目服务 │ 资产服务 │ 渲染服务 │ 协作服务        │
├─────────────────────────────────────────────────────────────┤
│                      AI Services Layer                      │
│ RAG服务 │ 语音服务 │ 数字人服务 │ 推荐服务 │ AI模型服务     │
├─────────────────────────────────────────────────────────────┤
│                   Blockchain & Edge Layer                   │
│ 区块链服务 │ 边缘游戏服务器 │ 信令服务                     │
├─────────────────────────────────────────────────────────────┤
│                     Infrastructure Layer                    │
│ MySQL │ Redis │ Elasticsearch │ MinIO │ 服务注册中心        │
└─────────────────────────────────────────────────────────────┘
```

## 系统要求

### 集群要求
- **Kubernetes版本**: 1.24+（推荐1.28+）
- **节点数量**: 最少3个节点（推荐5+个节点）
- **节点规格**: 
  - Master节点: 4核8GB（推荐8核16GB）
  - Worker节点: 8核16GB（推荐16核32GB）
- **存储**: 支持ReadWriteMany的存储类（NFS、Ceph、GlusterFS等）
- **网络**: CNI插件（Calico、Flannel、Weave等）

### 必需组件
- **Helm**: 3.0+
- **Ingress Controller**: Nginx Ingress、Traefik或Istio
- **存储类**: 动态存储供应
- **监控**: Prometheus Operator（可选）
- **日志**: ELK Stack或Loki（可选）

## 快速部署

### 1. 集群准备

```bash
# 检查集群状态
kubectl cluster-info
kubectl get nodes

# 创建命名空间
kubectl create namespace dl-engine-core
kubectl create namespace dl-engine-ai
kubectl create namespace dl-engine-blockchain
kubectl create namespace dl-engine-edge
kubectl create namespace dl-engine-monitoring
kubectl create namespace dl-engine-storage
```

### 2. 存储配置

```bash
# 创建存储类（示例：NFS）
cat <<EOF | kubectl apply -f -
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: nfs-storage
provisioner: nfs.csi.k8s.io
parameters:
  server: your-nfs-server
  share: /path/to/nfs/share
reclaimPolicy: Retain
allowVolumeExpansion: true
volumeBindingMode: Immediate
EOF
```

### 3. 配置管理

```bash
# 创建全局配置
kubectl create configmap dl-engine-global-config \
  --namespace=dl-engine-core \
  --from-literal=NODE_ENV=production \
  --from-literal=LOG_LEVEL=info \
  --from-literal=CLUSTER_NAME=dl-engine-cluster

# 创建密钥
kubectl create secret generic dl-engine-secrets \
  --namespace=dl-engine-core \
  --from-literal=MYSQL_ROOT_PASSWORD=your_mysql_password \
  --from-literal=JWT_SECRET=your_jwt_secret \
  --from-literal=REDIS_PASSWORD=your_redis_password \
  --from-literal=OPENAI_API_KEY=your_openai_key \
  --from-literal=AZURE_SPEECH_KEY=your_azure_speech_key
```

### 4. Helm仓库配置

```bash
# 添加必要的Helm仓库
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo add elastic https://helm.elastic.co
helm repo update
```

### 5. 基础设施部署

```bash
# 部署MySQL集群
helm install mysql bitnami/mysql \
  --namespace dl-engine-storage \
  --set auth.rootPassword="your_mysql_password" \
  --set auth.database="ir_engine" \
  --set primary.persistence.size="100Gi" \
  --set primary.persistence.storageClass="nfs-storage" \
  --set secondary.replicaCount=2 \
  --set secondary.persistence.size="100Gi" \
  --set secondary.persistence.storageClass="nfs-storage"

# 部署Redis集群
helm install redis bitnami/redis \
  --namespace dl-engine-storage \
  --set auth.password="your_redis_password" \
  --set master.persistence.size="20Gi" \
  --set master.persistence.storageClass="nfs-storage" \
  --set replica.replicaCount=3 \
  --set replica.persistence.size="20Gi" \
  --set replica.persistence.storageClass="nfs-storage"

# 部署Elasticsearch
helm install elasticsearch elastic/elasticsearch \
  --namespace dl-engine-storage \
  --set replicas=3 \
  --set minimumMasterNodes=2 \
  --set volumeClaimTemplate.resources.requests.storage="50Gi" \
  --set volumeClaimTemplate.storageClassName="nfs-storage"

# 部署MinIO
helm install minio bitnami/minio \
  --namespace dl-engine-storage \
  --set auth.rootUser="admin" \
  --set auth.rootPassword="your_minio_password" \
  --set persistence.size="200Gi" \
  --set persistence.storageClass="nfs-storage"
```

### 6. 核心服务部署

```bash
# 部署服务注册中心
kubectl apply -f k8s/core/service-registry.yaml

# 等待服务注册中心就绪
kubectl wait --for=condition=ready pod -l app=service-registry -n dl-engine-core --timeout=300s

# 部署核心微服务
kubectl apply -f k8s/core/user-service.yaml
kubectl apply -f k8s/core/project-service.yaml
kubectl apply -f k8s/core/asset-service.yaml
kubectl apply -f k8s/core/render-service.yaml
kubectl apply -f k8s/core/collaboration-service.yaml

# 部署API网关
kubectl apply -f k8s/core/api-gateway.yaml

# 部署前端编辑器
kubectl apply -f k8s/core/editor.yaml
```

### 7. AI服务部署

```bash
# 部署AI智能服务
kubectl apply -f k8s/ai/knowledge-base-service.yaml
kubectl apply -f k8s/ai/rag-dialogue-service.yaml
kubectl apply -f k8s/ai/avatar-service.yaml
kubectl apply -f k8s/ai/voice-service.yaml
kubectl apply -f k8s/ai/recommendation-service.yaml
kubectl apply -f k8s/ai/ai-model-service.yaml
```

### 8. 区块链和边缘服务部署

```bash
# 部署区块链服务
kubectl apply -f k8s/blockchain/blockchain-service.yaml

# 部署边缘计算服务
kubectl apply -f k8s/edge/edge-game-server.yaml
kubectl apply -f k8s/edge/signaling-service.yaml
```

### 9. Ingress配置

```bash
# 部署Nginx Ingress Controller
helm install ingress-nginx ingress-nginx/ingress-nginx \
  --namespace ingress-nginx \
  --create-namespace \
  --set controller.replicaCount=2 \
  --set controller.nodeSelector."kubernetes\.io/os"=linux \
  --set defaultBackend.nodeSelector."kubernetes\.io/os"=linux

# 应用Ingress规则
kubectl apply -f k8s/ingress/dl-engine-ingress.yaml
```

## 详细配置文件

### 核心服务配置示例

#### 服务注册中心部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: service-registry
  namespace: dl-engine-core
  labels:
    app: service-registry
    tier: infrastructure
spec:
  replicas: 2
  selector:
    matchLabels:
      app: service-registry
  template:
    metadata:
      labels:
        app: service-registry
    spec:
      containers:
      - name: service-registry
        image: dl-engine/service-registry:latest
        ports:
        - containerPort: 3010
        - containerPort: 4010
        env:
        - name: NODE_ENV
          value: "production"
        - name: DB_HOST
          value: "mysql.dl-engine-storage.svc.cluster.local"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: MYSQL_ROOT_PASSWORD
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 4010
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 4010
          initialDelaySeconds: 5
          periodSeconds: 5
```

#### API网关部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: dl-engine-core
  labels:
    app: api-gateway
    tier: gateway
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: dl-engine/api-gateway:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: JWT_SECRET
        - name: SERVICE_REGISTRY_HOST
          value: "service-registry.dl-engine-core.svc.cluster.local"
        - name: USER_SERVICE_HOST
          value: "user-service.dl-engine-core.svc.cluster.local"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

### AI服务配置示例

#### RAG对话服务部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rag-dialogue-service
  namespace: dl-engine-ai
  labels:
    app: rag-dialogue-service
    tier: ai-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: rag-dialogue-service
  template:
    metadata:
      labels:
        app: rag-dialogue-service
    spec:
      containers:
      - name: rag-dialogue-service
        image: dl-engine/rag-dialogue-service:latest
        ports:
        - containerPort: 4012
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "4012"
        - name: ELASTICSEARCH_URL
          value: "http://elasticsearch-master.dl-engine-storage.svc.cluster.local:9200"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: OPENAI_API_KEY
        - name: KNOWLEDGE_SERVICE_URL
          value: "http://knowledge-base-service.dl-engine-ai.svc.cluster.local:4011"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        volumeMounts:
        - name: temp-storage
          mountPath: /tmp/rag
        livenessProbe:
          httpGet:
            path: /health
            port: 4012
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 4012
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: temp-storage
        emptyDir:
          sizeLimit: 5Gi
```

#### 语音服务部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: voice-service
  namespace: dl-engine-ai
  labels:
    app: voice-service
    tier: ai-backend
spec:
  replicas: 2
  selector:
    matchLabels:
      app: voice-service
  template:
    metadata:
      labels:
        app: voice-service
    spec:
      containers:
      - name: voice-service
        image: dl-engine/voice-service:latest
        ports:
        - containerPort: 4014
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "4014"
        - name: AZURE_SPEECH_KEY
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: AZURE_SPEECH_KEY
        - name: AZURE_SPEECH_REGION
          value: "eastus"
        - name: OPENAI_API_KEY
          valueFrom:
            secretKeyRef:
              name: dl-engine-secrets
              key: OPENAI_API_KEY
        resources:
          requests:
            memory: "1.5Gi"
            cpu: "750m"
          limits:
            memory: "3Gi"
            cpu: "1500m"
        volumeMounts:
        - name: voice-uploads
          mountPath: /app/uploads
        - name: voice-temp
          mountPath: /app/temp
        livenessProbe:
          httpGet:
            path: /health
            port: 4014
          initialDelaySeconds: 45
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 4014
          initialDelaySeconds: 20
          periodSeconds: 10
      volumes:
      - name: voice-uploads
        persistentVolumeClaim:
          claimName: voice-uploads-pvc
      - name: voice-temp
        emptyDir:
          sizeLimit: 2Gi
```

### 边缘计算配置示例

#### 边缘游戏服务器部署
```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: edge-game-server
  namespace: dl-engine-edge
  labels:
    app: edge-game-server
    tier: edge-computing
spec:
  replicas: 3
  selector:
    matchLabels:
      app: edge-game-server
  template:
    metadata:
      labels:
        app: edge-game-server
    spec:
      containers:
      - name: edge-game-server
        image: dl-engine/edge-game-server:latest
        ports:
        - containerPort: 8080
        - containerPort: 3030
        - containerPort: 10000
          protocol: UDP
        env:
        - name: NODE_ENV
          value: "production"
        - name: EDGE_NODE_ID
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: EDGE_REGION
          value: "main"
        - name: CENTRAL_HUB_URL
          value: "http://api-gateway.dl-engine-core.svc.cluster.local:3000"
        - name: MAX_USERS_PER_EDGE
          value: "100"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        volumeMounts:
        - name: edge-cache
          mountPath: /app/cache
        - name: edge-logs
          mountPath: /app/logs
        livenessProbe:
          httpGet:
            path: /api/edge/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /api/edge/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: edge-cache
        emptyDir:
          sizeLimit: 10Gi
      - name: edge-logs
        emptyDir:
          sizeLimit: 1Gi
```

## 自动扩缩容配置

### 水平Pod自动扩缩容（HPA）

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
  namespace: dl-engine-core
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
```

### 垂直Pod自动扩缩容（VPA）

```yaml
apiVersion: autoscaling.k8s.io/v1
kind: VerticalPodAutoscaler
metadata:
  name: rag-dialogue-service-vpa
  namespace: dl-engine-ai
spec:
  targetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: rag-dialogue-service
  updatePolicy:
    updateMode: "Auto"
  resourcePolicy:
    containerPolicies:
    - containerName: rag-dialogue-service
      minAllowed:
        cpu: 500m
        memory: 1Gi
      maxAllowed:
        cpu: 4000m
        memory: 8Gi
```

## 监控部署

### Prometheus监控

```bash
# 部署Prometheus Operator
helm install prometheus prometheus-community/kube-prometheus-stack \
  --namespace dl-engine-monitoring \
  --set prometheus.prometheusSpec.retention=30d \
  --set prometheus.prometheusSpec.storageSpec.volumeClaimTemplate.spec.resources.requests.storage=100Gi \
  --set grafana.adminPassword="your_grafana_password" \
  --set grafana.persistence.enabled=true \
  --set grafana.persistence.size=20Gi
```

### 自定义监控指标

```yaml
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: dl-engine-services
  namespace: dl-engine-monitoring
spec:
  selector:
    matchLabels:
      monitoring: enabled
  endpoints:
  - port: http-metrics
    path: /metrics
    interval: 30s
```

## 网络策略

### 安全网络策略

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: dl-engine-core-network-policy
  namespace: dl-engine-core
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    - namespaceSelector:
        matchLabels:
          name: dl-engine-ai
    ports:
    - protocol: TCP
      port: 3000
    - protocol: TCP
      port: 3001
    - protocol: TCP
      port: 3002
  egress:
  - to:
    - namespaceSelector:
        matchLabels:
          name: dl-engine-storage
    ports:
    - protocol: TCP
      port: 3306
    - protocol: TCP
      port: 6379
    - protocol: TCP
      port: 9200
  - to: []
    ports:
    - protocol: TCP
      port: 53
    - protocol: UDP
      port: 53
```

## 备份与恢复

### 数据备份策略

```yaml
apiVersion: batch/v1
kind: CronJob
metadata:
  name: mysql-backup
  namespace: dl-engine-storage
spec:
  schedule: "0 2 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: mysql-backup
            image: mysql:8.0
            command:
            - /bin/bash
            - -c
            - |
              mysqldump -h mysql.dl-engine-storage.svc.cluster.local \
                -u root -p$MYSQL_ROOT_PASSWORD \
                --all-databases > /backup/mysql-$(date +%Y%m%d-%H%M%S).sql
            env:
            - name: MYSQL_ROOT_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: dl-engine-secrets
                  key: MYSQL_ROOT_PASSWORD
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure
```

## 故障排除

### 常见问题诊断

```bash
# 检查Pod状态
kubectl get pods -A

# 查看Pod日志
kubectl logs -f deployment/api-gateway -n dl-engine-core

# 检查服务端点
kubectl get endpoints -n dl-engine-core

# 检查资源使用情况
kubectl top nodes
kubectl top pods -A

# 检查存储状态
kubectl get pvc -A
kubectl get pv

# 检查网络连接
kubectl exec -it deployment/api-gateway -n dl-engine-core -- curl http://mysql.dl-engine-storage.svc.cluster.local:3306
```

### 性能调优

```bash
# 调整资源限制
kubectl patch deployment api-gateway -n dl-engine-core -p '{"spec":{"template":{"spec":{"containers":[{"name":"api-gateway","resources":{"limits":{"cpu":"2000m","memory":"4Gi"}}}]}}}}'

# 调整副本数量
kubectl scale deployment api-gateway --replicas=5 -n dl-engine-core

# 查看HPA状态
kubectl get hpa -A
```

## 升级策略

### 滚动更新

```bash
# 更新镜像
kubectl set image deployment/api-gateway api-gateway=dl-engine/api-gateway:v2.0.0 -n dl-engine-core

# 查看更新状态
kubectl rollout status deployment/api-gateway -n dl-engine-core

# 回滚更新
kubectl rollout undo deployment/api-gateway -n dl-engine-core
```

### 蓝绿部署

```yaml
apiVersion: argoproj.io/v1alpha1
kind: Rollout
metadata:
  name: api-gateway-rollout
  namespace: dl-engine-core
spec:
  replicas: 3
  strategy:
    blueGreen:
      activeService: api-gateway-active
      previewService: api-gateway-preview
      autoPromotionEnabled: false
      scaleDownDelaySeconds: 30
      prePromotionAnalysis:
        templates:
        - templateName: success-rate
        args:
        - name: service-name
          value: api-gateway-preview
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: dl-engine/api-gateway:latest
```

## 多环境管理

### 环境配置

```bash
# 开发环境
kubectl apply -k k8s/overlays/development

# 测试环境
kubectl apply -k k8s/overlays/staging

# 生产环境
kubectl apply -k k8s/overlays/production
```

### Kustomization配置

```yaml
# k8s/overlays/production/kustomization.yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../base

patchesStrategicMerge:
- replica-count.yaml
- resource-limits.yaml

images:
- name: dl-engine/api-gateway
  newTag: v1.0.0
- name: dl-engine/user-service
  newTag: v1.0.0

configMapGenerator:
- name: dl-engine-config
  literals:
  - NODE_ENV=production
  - LOG_LEVEL=warn
```

## 安全配置

### RBAC配置

```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: dl-engine-operator
rules:
- apiGroups: [""]
  resources: ["pods", "services", "configmaps", "secrets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch", "create", "update", "patch", "delete"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: dl-engine-operator-binding
subjects:
- kind: ServiceAccount
  name: dl-engine-operator
  namespace: dl-engine-core
roleRef:
  kind: ClusterRole
  name: dl-engine-operator
  apiGroup: rbac.authorization.k8s.io
```

### Pod安全策略

```yaml
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: dl-engine-psp
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  runAsUser:
    rule: 'MustRunAsNonRoot'
  seLinux:
    rule: 'RunAsAny'
  fsGroup:
    rule: 'RunAsAny'
```

## 相关文档

- [Docker Compose部署指南](./DL引擎Docker Compose部署指南.md)
- [边缘计算部署指南](./边缘计算部署指南.md)
- [区块链集成技术实施指南](./DL引擎区块链集成技术实施指南.md)
- [API文档](../api/README.md)
- [开发者指南](../developer/README.md)

## 完整部署脚本

### 自动化部署脚本

```bash
#!/bin/bash
# deploy-dl-engine-k8s.sh - DL引擎Kubernetes自动化部署脚本

set -e

# 配置变量
NAMESPACE_CORE="dl-engine-core"
NAMESPACE_AI="dl-engine-ai"
NAMESPACE_BLOCKCHAIN="dl-engine-blockchain"
NAMESPACE_EDGE="dl-engine-edge"
NAMESPACE_MONITORING="dl-engine-monitoring"
NAMESPACE_STORAGE="dl-engine-storage"

REGISTRY="${REGISTRY:-your-registry.com}"
VERSION="${VERSION:-latest}"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."

    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装"
        exit 1
    fi

    if ! command -v helm &> /dev/null; then
        log_error "helm 未安装"
        exit 1
    fi

    # 检查集群连接
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到Kubernetes集群"
        exit 1
    fi

    log_info "依赖检查完成"
}

# 创建命名空间
create_namespaces() {
    log_info "创建命名空间..."

    kubectl create namespace $NAMESPACE_CORE --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace $NAMESPACE_AI --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace $NAMESPACE_BLOCKCHAIN --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace $NAMESPACE_EDGE --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace $NAMESPACE_MONITORING --dry-run=client -o yaml | kubectl apply -f -
    kubectl create namespace $NAMESPACE_STORAGE --dry-run=client -o yaml | kubectl apply -f -

    # 添加标签
    kubectl label namespace $NAMESPACE_CORE name=dl-engine-core --overwrite
    kubectl label namespace $NAMESPACE_AI name=dl-engine-ai --overwrite
    kubectl label namespace $NAMESPACE_BLOCKCHAIN name=dl-engine-blockchain --overwrite
    kubectl label namespace $NAMESPACE_EDGE name=dl-engine-edge --overwrite
    kubectl label namespace $NAMESPACE_MONITORING name=dl-engine-monitoring --overwrite
    kubectl label namespace $NAMESPACE_STORAGE name=dl-engine-storage --overwrite

    log_info "命名空间创建完成"
}

# 部署基础设施
deploy_infrastructure() {
    log_info "部署基础设施..."

    # 添加Helm仓库
    helm repo add bitnami https://charts.bitnami.com/bitnami
    helm repo add elastic https://helm.elastic.co
    helm repo update

    # 部署MySQL
    log_info "部署MySQL..."
    helm upgrade --install mysql bitnami/mysql \
        --namespace $NAMESPACE_STORAGE \
        --set auth.rootPassword="$MYSQL_ROOT_PASSWORD" \
        --set auth.database="ir_engine" \
        --set primary.persistence.size="100Gi" \
        --set secondary.replicaCount=1 \
        --wait --timeout=600s

    # 部署Redis
    log_info "部署Redis..."
    helm upgrade --install redis bitnami/redis \
        --namespace $NAMESPACE_STORAGE \
        --set auth.password="$REDIS_PASSWORD" \
        --set master.persistence.size="20Gi" \
        --set replica.replicaCount=2 \
        --wait --timeout=600s

    # 部署Elasticsearch
    log_info "部署Elasticsearch..."
    helm upgrade --install elasticsearch elastic/elasticsearch \
        --namespace $NAMESPACE_STORAGE \
        --set replicas=3 \
        --set minimumMasterNodes=2 \
        --set volumeClaimTemplate.resources.requests.storage="50Gi" \
        --wait --timeout=600s

    log_info "基础设施部署完成"
}

# 部署核心服务
deploy_core_services() {
    log_info "部署核心服务..."

    # 创建配置和密钥
    create_configs_and_secrets

    # 部署服务注册中心
    log_info "部署服务注册中心..."
    kubectl apply -f k8s/core/service-registry.yaml
    kubectl wait --for=condition=ready pod -l app=service-registry -n $NAMESPACE_CORE --timeout=300s

    # 部署核心微服务
    log_info "部署核心微服务..."
    kubectl apply -f k8s/core/user-service.yaml
    kubectl apply -f k8s/core/project-service.yaml
    kubectl apply -f k8s/core/asset-service.yaml
    kubectl apply -f k8s/core/render-service.yaml
    kubectl apply -f k8s/core/collaboration-service.yaml

    # 等待服务就绪
    kubectl wait --for=condition=ready pod -l tier=backend -n $NAMESPACE_CORE --timeout=600s

    # 部署API网关
    log_info "部署API网关..."
    kubectl apply -f k8s/core/api-gateway.yaml
    kubectl wait --for=condition=ready pod -l app=api-gateway -n $NAMESPACE_CORE --timeout=300s

    # 部署前端编辑器
    log_info "部署前端编辑器..."
    kubectl apply -f k8s/core/editor.yaml

    log_info "核心服务部署完成"
}

# 部署AI服务
deploy_ai_services() {
    log_info "部署AI服务..."

    kubectl apply -f k8s/ai/
    kubectl wait --for=condition=ready pod -l tier=ai-backend -n $NAMESPACE_AI --timeout=600s

    log_info "AI服务部署完成"
}

# 部署区块链和边缘服务
deploy_blockchain_edge_services() {
    log_info "部署区块链和边缘服务..."

    kubectl apply -f k8s/blockchain/
    kubectl apply -f k8s/edge/

    log_info "区块链和边缘服务部署完成"
}

# 配置Ingress
configure_ingress() {
    log_info "配置Ingress..."

    # 部署Nginx Ingress Controller
    helm upgrade --install ingress-nginx ingress-nginx/ingress-nginx \
        --namespace ingress-nginx \
        --create-namespace \
        --set controller.replicaCount=2 \
        --wait --timeout=300s

    # 应用Ingress规则
    kubectl apply -f k8s/ingress/

    log_info "Ingress配置完成"
}

# 部署监控
deploy_monitoring() {
    log_info "部署监控..."

    helm upgrade --install prometheus prometheus-community/kube-prometheus-stack \
        --namespace $NAMESPACE_MONITORING \
        --set prometheus.prometheusSpec.retention=30d \
        --set grafana.adminPassword="$GRAFANA_PASSWORD" \
        --wait --timeout=600s

    log_info "监控部署完成"
}

# 创建配置和密钥
create_configs_and_secrets() {
    log_info "创建配置和密钥..."

    # 创建全局配置
    kubectl create configmap dl-engine-global-config \
        --namespace=$NAMESPACE_CORE \
        --from-literal=NODE_ENV=production \
        --from-literal=LOG_LEVEL=info \
        --from-literal=CLUSTER_NAME=dl-engine-cluster \
        --dry-run=client -o yaml | kubectl apply -f -

    # 创建密钥
    kubectl create secret generic dl-engine-secrets \
        --namespace=$NAMESPACE_CORE \
        --from-literal=MYSQL_ROOT_PASSWORD="$MYSQL_ROOT_PASSWORD" \
        --from-literal=JWT_SECRET="$JWT_SECRET" \
        --from-literal=REDIS_PASSWORD="$REDIS_PASSWORD" \
        --from-literal=OPENAI_API_KEY="$OPENAI_API_KEY" \
        --from-literal=AZURE_SPEECH_KEY="$AZURE_SPEECH_KEY" \
        --dry-run=client -o yaml | kubectl apply -f -

    # 复制密钥到其他命名空间
    for ns in $NAMESPACE_AI $NAMESPACE_BLOCKCHAIN $NAMESPACE_EDGE; do
        kubectl get secret dl-engine-secrets -n $NAMESPACE_CORE -o yaml | \
        sed "s/namespace: $NAMESPACE_CORE/namespace: $ns/" | \
        kubectl apply -f -
    done

    log_info "配置和密钥创建完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."

    # 检查Pod状态
    log_info "检查Pod状态..."
    kubectl get pods -A | grep -E "(dl-engine|mysql|redis|elasticsearch)"

    # 检查服务状态
    log_info "检查服务状态..."
    kubectl get svc -A | grep -E "(dl-engine|mysql|redis|elasticsearch)"

    # 健康检查
    log_info "执行健康检查..."

    # 等待API网关就绪
    kubectl wait --for=condition=ready pod -l app=api-gateway -n $NAMESPACE_CORE --timeout=300s

    # 测试API网关
    API_GATEWAY_IP=$(kubectl get svc api-gateway -n $NAMESPACE_CORE -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
    if [ -z "$API_GATEWAY_IP" ]; then
        API_GATEWAY_IP=$(kubectl get svc api-gateway -n $NAMESPACE_CORE -o jsonpath='{.spec.clusterIP}')
    fi

    if curl -f "http://$API_GATEWAY_IP:3000/api/health" &> /dev/null; then
        log_info "API网关健康检查通过"
    else
        log_warn "API网关健康检查失败"
    fi

    log_info "部署验证完成"
}

# 显示访问信息
show_access_info() {
    log_info "部署完成！访问信息："

    echo "=================================="
    echo "DL引擎 Kubernetes 部署完成"
    echo "=================================="

    # 获取Ingress信息
    INGRESS_IP=$(kubectl get ingress dl-engine-ingress -n $NAMESPACE_CORE -o jsonpath='{.status.loadBalancer.ingress[0].ip}' 2>/dev/null || echo "pending")

    echo "前端编辑器: http://$INGRESS_IP"
    echo "API文档: http://$INGRESS_IP/api/docs"
    echo "Grafana监控: http://$INGRESS_IP:3000 (admin/$GRAFANA_PASSWORD)"

    echo ""
    echo "命令行访问："
    echo "kubectl get pods -A"
    echo "kubectl logs -f deployment/api-gateway -n $NAMESPACE_CORE"
    echo ""
    echo "如需删除部署，运行："
    echo "./deploy-dl-engine-k8s.sh cleanup"
}

# 清理部署
cleanup_deployment() {
    log_warn "开始清理部署..."

    read -p "确定要删除所有DL引擎资源吗？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "取消清理操作"
        exit 0
    fi

    # 删除应用资源
    kubectl delete -f k8s/ --recursive --ignore-not-found=true

    # 删除Helm发布
    helm uninstall mysql -n $NAMESPACE_STORAGE --ignore-not-found
    helm uninstall redis -n $NAMESPACE_STORAGE --ignore-not-found
    helm uninstall elasticsearch -n $NAMESPACE_STORAGE --ignore-not-found
    helm uninstall prometheus -n $NAMESPACE_MONITORING --ignore-not-found
    helm uninstall ingress-nginx -n ingress-nginx --ignore-not-found

    # 删除命名空间
    kubectl delete namespace $NAMESPACE_CORE --ignore-not-found=true
    kubectl delete namespace $NAMESPACE_AI --ignore-not-found=true
    kubectl delete namespace $NAMESPACE_BLOCKCHAIN --ignore-not-found=true
    kubectl delete namespace $NAMESPACE_EDGE --ignore-not-found=true
    kubectl delete namespace $NAMESPACE_MONITORING --ignore-not-found=true
    kubectl delete namespace $NAMESPACE_STORAGE --ignore-not-found=true
    kubectl delete namespace ingress-nginx --ignore-not-found=true

    log_info "清理完成"
}

# 主函数
main() {
    case "${1:-deploy}" in
        "deploy")
            log_info "开始部署DL引擎到Kubernetes..."
            check_dependencies
            create_namespaces
            deploy_infrastructure
            deploy_core_services
            deploy_ai_services
            deploy_blockchain_edge_services
            configure_ingress
            deploy_monitoring
            verify_deployment
            show_access_info
            ;;
        "cleanup")
            cleanup_deployment
            ;;
        "verify")
            verify_deployment
            ;;
        *)
            echo "用法: $0 [deploy|cleanup|verify]"
            echo "  deploy  - 部署DL引擎 (默认)"
            echo "  cleanup - 清理所有资源"
            echo "  verify  - 验证部署状态"
            exit 1
            ;;
    esac
}

# 检查环境变量
if [ -z "$MYSQL_ROOT_PASSWORD" ] || [ -z "$JWT_SECRET" ] || [ -z "$REDIS_PASSWORD" ]; then
    log_error "请设置必需的环境变量："
    echo "export MYSQL_ROOT_PASSWORD=your_mysql_password"
    echo "export JWT_SECRET=your_jwt_secret"
    echo "export REDIS_PASSWORD=your_redis_password"
    echo "export OPENAI_API_KEY=your_openai_key"
    echo "export AZURE_SPEECH_KEY=your_azure_speech_key"
    echo "export GRAFANA_PASSWORD=your_grafana_password"
    exit 1
fi

# 执行主函数
main "$@"
```

## 技术支持

如需技术支持，请：
1. 查看本文档的故障排除部分
2. 检查Kubernetes集群日志
3. 联系技术支持团队
4. 参考官方文档和社区论坛
