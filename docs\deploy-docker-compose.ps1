# DL引擎 Docker Compose 自动化部署脚本 (PowerShell版本)
# 版本: 1.0.0
# 作者: DL Engine Team

param(
    [Parameter(Position=0)]
    [ValidateSet("deploy", "start", "stop", "restart", "status", "logs", "cleanup", "update", "backup", "restore", "health", "scale", "help")]
    [string]$Command = "deploy",
    
    [Parameter(Position=1)]
    [string]$ServiceName = "",
    
    [Parameter(Position=2)]
    [string]$BackupPath = "",
    
    [switch]$Force,
    [switch]$Verbose,
    [switch]$NoBuild,
    [switch]$Pull,
    [string]$ComposeFile = "",
    [string]$EnvFile = ""
)

# 配置变量
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir
$DefaultComposeFile = Join-Path $ScriptDir "docker-compose.complete.yml"
$DefaultEnvFile = Join-Path $ProjectRoot ".env"
$EnvTemplate = Join-Path $ScriptDir ".env.template"

# 使用参数或默认值
$ComposeFile = if ($ComposeFile) { $ComposeFile } else { $DefaultComposeFile }
$EnvFile = if ($EnvFile) { $EnvFile } else { $DefaultEnvFile }

# 颜色输出函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    
    $colorMap = @{
        "Red" = "Red"
        "Green" = "Green"
        "Yellow" = "Yellow"
        "Blue" = "Blue"
        "White" = "White"
    }
    
    Write-Host $Message -ForegroundColor $colorMap[$Color]
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Green"
}

function Write-Warn {
    param([string]$Message)
    Write-ColorOutput "[WARN] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

function Write-Debug {
    param([string]$Message)
    if ($Verbose) {
        Write-ColorOutput "[DEBUG] $Message" "Blue"
    }
}

# 显示帮助信息
function Show-Help {
    @"
DL引擎 Docker Compose 部署脚本 (PowerShell版本)

用法: .\deploy-docker-compose.ps1 [选项] [命令]

命令:
  deploy      部署所有服务 (默认)
  start       启动所有服务
  stop        停止所有服务
  restart     重启所有服务
  status      查看服务状态
  logs        查看服务日志
  cleanup     清理所有资源
  update      更新服务镜像
  backup      备份数据
  restore     恢复数据
  health      健康检查
  scale       扩缩容服务

选项:
  -Force              强制执行操作
  -Verbose            详细输出
  -NoBuild            不重新构建镜像
  -Pull               拉取最新镜像
  -ComposeFile FILE   指定compose文件
  -EnvFile FILE       指定环境变量文件

示例:
  .\deploy-docker-compose.ps1 deploy               # 部署所有服务
  .\deploy-docker-compose.ps1 start                # 启动所有服务
  .\deploy-docker-compose.ps1 logs api-gateway     # 查看API网关日志
  .\deploy-docker-compose.ps1 cleanup -Force       # 强制清理所有资源

"@
}

# 检查依赖
function Test-Dependencies {
    Write-Info "检查依赖..."
    
    # 检查Docker
    try {
        $dockerVersion = docker --version
        Write-Debug "Docker版本: $dockerVersion"
    }
    catch {
        Write-Error "Docker 未安装或未启动，请先安装并启动 Docker Desktop"
        exit 1
    }
    
    # 检查Docker Compose
    try {
        $composeVersion = docker-compose --version
        Write-Debug "Docker Compose版本: $composeVersion"
    }
    catch {
        Write-Error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    }
    
    # 检查Docker服务状态
    try {
        docker info | Out-Null
    }
    catch {
        Write-Error "Docker 服务未运行，请启动 Docker Desktop"
        exit 1
    }
    
    Write-Info "依赖检查完成"
}

# 检查环境变量文件
function Test-EnvFile {
    Write-Info "检查环境变量文件..."
    
    if (-not (Test-Path $EnvFile)) {
        Write-Warn "环境变量文件不存在: $EnvFile"
        
        if (Test-Path $EnvTemplate) {
            Write-Info "复制环境变量模板..."
            Copy-Item $EnvTemplate $EnvFile
            Write-Warn "请编辑 $EnvFile 文件并填入正确的配置值"
            Write-Warn "必需配置项: MYSQL_ROOT_PASSWORD, REDIS_PASSWORD, JWT_SECRET, OPENAI_API_KEY"
            
            $response = Read-Host "是否现在编辑环境变量文件? (y/N)"
            if ($response -eq "y" -or $response -eq "Y") {
                notepad $EnvFile
            }
            else {
                Write-Error "请先配置环境变量文件后再运行部署"
                exit 1
            }
        }
        else {
            Write-Error "环境变量模板文件不存在: $EnvTemplate"
            exit 1
        }
    }
    
    # 验证必需的环境变量
    $envContent = Get-Content $EnvFile | Where-Object { $_ -match "^[^#].*=" }
    $envVars = @{}
    
    foreach ($line in $envContent) {
        if ($line -match "^([^=]+)=(.*)$") {
            $envVars[$matches[1]] = $matches[2]
        }
    }
    
    $requiredVars = @("MYSQL_ROOT_PASSWORD", "REDIS_PASSWORD", "JWT_SECRET")
    $missingVars = @()
    
    foreach ($var in $requiredVars) {
        if (-not $envVars.ContainsKey($var) -or [string]::IsNullOrWhiteSpace($envVars[$var])) {
            $missingVars += $var
        }
    }
    
    if ($missingVars.Count -gt 0) {
        Write-Error "缺少必需的环境变量: $($missingVars -join ', ')"
        Write-Error "请编辑 $EnvFile 文件并设置这些变量"
        exit 1
    }
    
    Write-Info "环境变量检查完成"
}

# 检查系统资源
function Test-SystemResources {
    Write-Info "检查系统资源..."
    
    # 检查可用内存
    $memory = Get-CimInstance -ClassName Win32_OperatingSystem
    $availableMemoryGB = [math]::Round($memory.FreePhysicalMemory / 1MB, 2)
    $requiredMemoryGB = 8
    
    if ($availableMemoryGB -lt $requiredMemoryGB) {
        Write-Warn "可用内存不足: ${availableMemoryGB}GB (推荐: ${requiredMemoryGB}GB)"
        Write-Warn "系统可能运行缓慢，建议增加内存或调整服务配置"
    }
    
    # 检查磁盘空间
    $disk = Get-CimInstance -ClassName Win32_LogicalDisk | Where-Object { $_.DeviceID -eq "C:" }
    $availableDiskGB = [math]::Round($disk.FreeSpace / 1GB, 2)
    $requiredDiskGB = 20
    
    if ($availableDiskGB -lt $requiredDiskGB) {
        Write-Warn "可用磁盘空间不足: ${availableDiskGB}GB (推荐: ${requiredDiskGB}GB)"
        Write-Warn "可能影响数据存储和日志记录"
    }
    
    Write-Info "系统资源检查完成"
}

# 创建必要的目录
function New-RequiredDirectories {
    Write-Info "创建必要的目录..."
    
    $dirs = @(
        "$ProjectRoot\uploads\knowledge",
        "$ProjectRoot\uploads\voice",
        "$ProjectRoot\uploads\avatars",
        "$ProjectRoot\temp\knowledge",
        "$ProjectRoot\temp\voice",
        "$ProjectRoot\logs",
        "$ProjectRoot\models\recommendation",
        "$ProjectRoot\models\ai",
        "$ProjectRoot\config\mysql",
        "$ProjectRoot\config\redis",
        "$ProjectRoot\nginx\conf.d",
        "$ProjectRoot\ssl"
    )
    
    foreach ($dir in $dirs) {
        if (-not (Test-Path $dir)) {
            New-Item -ItemType Directory -Path $dir -Force | Out-Null
            Write-Debug "创建目录: $dir"
        }
    }
    
    Write-Info "目录创建完成"
}

# 部署服务
function Start-Deployment {
    Write-Info "开始部署DL引擎服务..."
    
    # 检查依赖
    Test-Dependencies
    Test-EnvFile
    Test-SystemResources
    New-RequiredDirectories
    
    # 构建和启动服务
    Write-Info "构建和启动基础设施服务..."
    docker-compose -f $ComposeFile --env-file $EnvFile up -d mysql redis elasticsearch minio
    
    # 等待基础设施就绪
    Write-Info "等待基础设施服务启动..."
    Start-Sleep -Seconds 30
    
    # 启动核心微服务
    Write-Info "启动核心微服务..."
    docker-compose -f $ComposeFile --env-file $EnvFile up -d service-registry
    Start-Sleep -Seconds 15
    
    docker-compose -f $ComposeFile --env-file $EnvFile up -d user-service project-service asset-service render-service
    Start-Sleep -Seconds 20
    
    # 启动AI服务
    Write-Info "启动AI智能服务..."
    docker-compose -f $ComposeFile --env-file $EnvFile up -d knowledge-base-service rag-dialogue-service avatar-service voice-service recommendation-service ai-model-service
    Start-Sleep -Seconds 15
    
    # 启动协作服务
    Write-Info "启动协作服务..."
    docker-compose -f $ComposeFile --env-file $EnvFile up -d collaboration-service-1 collaboration-service-2 collaboration-load-balancer
    Start-Sleep -Seconds 10
    
    # 启动区块链和边缘服务
    Write-Info "启动区块链和边缘服务..."
    docker-compose -f $ComposeFile --env-file $EnvFile up -d blockchain-service edge-game-server signaling-service
    Start-Sleep -Seconds 10
    
    # 启动API网关和前端
    Write-Info "启动API网关和前端..."
    docker-compose -f $ComposeFile --env-file $EnvFile up -d api-gateway editor
    Start-Sleep -Seconds 10
    
    # 启动监控和代理服务
    Write-Info "启动监控和代理服务..."
    docker-compose -f $ComposeFile --env-file $EnvFile up -d monitoring-service nginx
    
    Write-Info "所有服务部署完成"
    
    # 显示访问信息
    Show-AccessInfo
}

# 启动服务
function Start-Services {
    Write-Info "启动所有服务..."
    docker-compose -f $ComposeFile --env-file $EnvFile start
    Write-Info "服务启动完成"
}

# 停止服务
function Stop-Services {
    Write-Info "停止所有服务..."
    docker-compose -f $ComposeFile --env-file $EnvFile stop
    Write-Info "服务停止完成"
}

# 重启服务
function Restart-Services {
    Write-Info "重启所有服务..."
    docker-compose -f $ComposeFile --env-file $EnvFile restart
    Write-Info "服务重启完成"
}

# 查看服务状态
function Show-Status {
    Write-Info "服务状态:"
    docker-compose -f $ComposeFile --env-file $EnvFile ps
}

# 查看服务日志
function Show-Logs {
    param([string]$Service)
    
    if ($Service) {
        Write-Info "查看 $Service 服务日志:"
        docker-compose -f $ComposeFile --env-file $EnvFile logs -f $Service
    }
    else {
        Write-Info "查看所有服务日志:"
        docker-compose -f $ComposeFile --env-file $EnvFile logs -f
    }
}

# 清理资源
function Remove-Resources {
    param([bool]$ForceCleanup)
    
    if (-not $ForceCleanup) {
        Write-Warn "此操作将删除所有容器、网络和数据卷"
        $response = Read-Host "确定要继续吗? (y/N)"
        if ($response -ne "y" -and $response -ne "Y") {
            Write-Info "操作已取消"
            return
        }
    }
    
    Write-Info "清理所有资源..."
    
    # 停止并删除容器
    docker-compose -f $ComposeFile --env-file $EnvFile down -v --remove-orphans
    
    # 删除未使用的镜像
    docker image prune -f
    
    # 删除未使用的网络
    docker network prune -f
    
    # 删除未使用的数据卷
    docker volume prune -f
    
    Write-Info "资源清理完成"
}

# 健康检查
function Test-Health {
    Write-Info "执行健康检查..."
    
    $services = @(
        @{Name="mysql"; Port=3306},
        @{Name="redis"; Port=6379},
        @{Name="elasticsearch"; Port=9200},
        @{Name="api-gateway"; Port=3000},
        @{Name="user-service"; Port=4001},
        @{Name="project-service"; Port=4002},
        @{Name="asset-service"; Port=4003},
        @{Name="render-service"; Port=4004},
        @{Name="knowledge-base-service"; Port=4011},
        @{Name="rag-dialogue-service"; Port=4012},
        @{Name="avatar-service"; Port=4013},
        @{Name="voice-service"; Port=4014},
        @{Name="editor"; Port=80}
    )
    
    $failedServices = @()
    
    foreach ($service in $services) {
        try {
            $response = Invoke-WebRequest -Uri "http://localhost:$($service.Port)/health" -TimeoutSec 5 -UseBasicParsing
            if ($response.StatusCode -eq 200) {
                Write-Info "✓ $($service.Name) 健康"
            }
            else {
                Write-Error "✗ $($service.Name) 不健康 (状态码: $($response.StatusCode))"
                $failedServices += $service.Name
            }
        }
        catch {
            Write-Error "✗ $($service.Name) 不健康 (连接失败)"
            $failedServices += $service.Name
        }
    }
    
    if ($failedServices.Count -eq 0) {
        Write-Info "所有服务健康检查通过"
    }
    else {
        Write-Error "以下服务健康检查失败: $($failedServices -join ', ')"
        exit 1
    }
}

# 显示访问信息
function Show-AccessInfo {
    Write-Info "部署完成！访问信息："
    
    @"

================================
DL引擎服务访问信息
================================

前端应用:
  编辑器界面:     http://localhost
  HTTPS访问:      https://localhost

API服务:
  API网关:        http://localhost:3000
  API文档:        http://localhost:3000/api/docs

AI智能服务:
  知识库服务:     http://localhost:4011
  RAG对话服务:    http://localhost:4012
  数字人服务:     http://localhost:4013
  语音服务:       http://localhost:4014

基础设施:
  MySQL:          localhost:3306
  Redis:          localhost:6379
  Elasticsearch:  http://localhost:9200
  MinIO控制台:    http://localhost:9001

常用命令:
  查看服务状态:   .\deploy-docker-compose.ps1 status
  查看日志:       .\deploy-docker-compose.ps1 logs [service-name]
  健康检查:       .\deploy-docker-compose.ps1 health
  停止服务:       .\deploy-docker-compose.ps1 stop
  重启服务:       .\deploy-docker-compose.ps1 restart

================================

"@
}

# 主函数
switch ($Command) {
    "deploy" { Start-Deployment }
    "start" { Start-Services }
    "stop" { Stop-Services }
    "restart" { Restart-Services }
    "status" { Show-Status }
    "logs" { Show-Logs -Service $ServiceName }
    "cleanup" { Remove-Resources -ForceCleanup $Force }
    "health" { Test-Health }
    "help" { Show-Help }
    default {
        Write-Error "未知命令: $Command"
        Show-Help
        exit 1
    }
}
