/**
 * 数字人路径跟随组件
 * 将路径跟随功能与数字人系统集成
 */
import { Component } from '../../core/Component';
import { Entity } from '../../core/Entity';
import { PathFollowingComponent, PathFollowingState } from '../../navigation/components/PathFollowingComponent';
import { AvatarPath } from '../../navigation/path/AvatarPath';
import { PathEventType, PathEventData } from '../../navigation/types';
import { EnhancedAvatarComponent } from './EnhancedAvatarComponent';
import { EventEmitter } from '../../utils/EventEmitter';
import * as Logger from '../../utils/Logger'
//import { Logger } from '../../utils/Logger';
import * as THREE from 'three';

/**
 * 数字人路径跟随选项
 */
export interface AvatarPathFollowingOptions {
  /** 路径数据 */
  path?: AvatarPath;
  /** 是否自动开始 */
  autoStart?: boolean;
  /** 速度倍数 */
  speedMultiplier?: number;
  /** 是否循环 */
  loop?: boolean;
  /** 是否启用动画过渡 */
  enableAnimationTransition?: boolean;
  /** 动画过渡时间 */
  animationTransitionTime?: number;
  /** 是否启用朝向插值 */
  enableLookAtInterpolation?: boolean;
  /** 朝向插值速度 */
  lookAtInterpolationSpeed?: number;
  /** 是否启用路径事件处理 */
  enablePathEvents?: boolean;
  /** 是否启用调试 */
  debug?: boolean;
}

/**
 * 数字人路径跟随组件
 */
export class AvatarPathFollowingComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE = 'AvatarPathFollowingComponent';

  private logger = new Logger('AvatarPathFollowingComponent');
  private eventEmitter = new EventEmitter();
  
  // 组件引用
  private pathFollowingComponent: PathFollowingComponent | null = null;
  private avatarComponent: EnhancedAvatarComponent | null = null;
  
  // 配置选项
  private options: AvatarPathFollowingOptions;
  
  // 状态管理
  private currentAnimation: string = '';
  private previousAnimation: string = '';
  private isTransitioning: boolean = false;
  private currentLookAt: THREE.Vector3 | null = null;
  private targetLookAt: THREE.Vector3 | null = null;
  
  // 插值相关
  private lookAtInterpolationT: number = 0;
  private animationTransitionT: number = 0;

  /**
   * 构造函数
   * @param entity 实体
   * @param options 选项
   */
  constructor(entity: Entity, options: AvatarPathFollowingOptions = {}) {
    super(AvatarPathFollowingComponent.TYPE);

    // 设置实体引用
    this.setEntity(entity);

    this.options = {
      autoStart: false,
      speedMultiplier: 1.0,
      loop: false,
      enableAnimationTransition: true,
      animationTransitionTime: 0.3,
      enableLookAtInterpolation: true,
      lookAtInterpolationSpeed: 2.0,
      enablePathEvents: true,
      debug: false,
      ...options
    };

    this.initialize();
  }

  /**
   * 初始化组件
   */
  private async initialize(): Promise<void> {
    try {
      // 获取数字人组件
      this.avatarComponent = this.entity?.getComponent(EnhancedAvatarComponent.TYPE) as EnhancedAvatarComponent | null;
      if (!this.avatarComponent) {
        this.logger.warn('实体没有数字人组件，路径跟随功能可能受限');
      }

      // 创建路径跟随组件
      this.pathFollowingComponent = new PathFollowingComponent(this.entity, {
        path: this.options.path?.toJSON(),
        loop: this.options.loop,
        speedMultiplier: this.options.speedMultiplier,
        paused: !this.options.autoStart
      });

      // 添加路径跟随组件到实体
      this.entity?.addComponent(this.pathFollowingComponent as any);

      // 设置事件监听
      this.setupEventListeners();

      // 如果有路径且自动开始，则开始跟随
      if (this.options.path && this.options.autoStart) {
        this.startFollowing();
      }

      this.logger.info('数字人路径跟随组件初始化完成', { entityId: this.entity?.id });

    } catch (error) {
      this.logger.error('初始化数字人路径跟随组件失败', error);
    }
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    if (!this.pathFollowingComponent) return;

    // 路径开始事件
    this.pathFollowingComponent.on(PathEventType.PATH_STARTED, (data: PathEventData) => {
      this.handlePathStarted(data);
    });

    // 路径完成事件
    this.pathFollowingComponent.on(PathEventType.PATH_COMPLETED, (data: PathEventData) => {
      this.handlePathCompleted(data);
    });

    // 到达路径点事件
    this.pathFollowingComponent.on(PathEventType.WAYPOINT_REACHED, (data: PathEventData) => {
      this.handleWaypointReached(data);
    });

    // 触发器激活事件
    this.pathFollowingComponent.on(PathEventType.TRIGGER_ACTIVATED, (data: PathEventData) => {
      this.handleTriggerActivated(data);
    });

    // 路径暂停/恢复事件
    this.pathFollowingComponent.on(PathEventType.PATH_PAUSED, (data: PathEventData) => {
      this.handlePathPaused(data);
    });

    this.pathFollowingComponent.on(PathEventType.PATH_RESUMED, (data: PathEventData) => {
      this.handlePathResumed(data);
    });
  }

  /**
   * 设置路径
   * @param path 路径数据
   */
  public setPath(path: AvatarPath): void {
    this.options.path = path;
    
    if (this.pathFollowingComponent) {
      this.pathFollowingComponent.setPath(path);
    }

    this.eventEmitter.emit('pathSet', { path });
  }

  /**
   * 开始跟随路径
   */
  public startFollowing(): void {
    if (!this.pathFollowingComponent) {
      this.logger.warn('路径跟随组件未初始化');
      return;
    }

    this.pathFollowingComponent.start();
    this.logger.info('开始路径跟随', { entityId: this.entity?.id });
  }

  /**
   * 停止跟随路径
   */
  public stopFollowing(): void {
    if (!this.pathFollowingComponent) return;

    this.pathFollowingComponent.stop();
    
    // 重置状态
    this.currentAnimation = '';
    this.previousAnimation = '';
    this.isTransitioning = false;
    this.currentLookAt = null;
    this.targetLookAt = null;

    this.logger.info('停止路径跟随', { entityId: this.entity?.id });
  }

  /**
   * 暂停跟随路径
   */
  public pauseFollowing(): void {
    if (!this.pathFollowingComponent) return;
    this.pathFollowingComponent.pause();
  }

  /**
   * 恢复跟随路径
   */
  public resumeFollowing(): void {
    if (!this.pathFollowingComponent) return;
    this.pathFollowingComponent.resume();
  }

  /**
   * 设置速度倍数
   * @param multiplier 速度倍数
   */
  public setSpeedMultiplier(multiplier: number): void {
    this.options.speedMultiplier = multiplier;
    if (this.pathFollowingComponent) {
      this.pathFollowingComponent.setSpeedMultiplier(multiplier);
    }
  }

  /**
   * 设置是否循环
   * @param loop 是否循环
   */
  public setLoop(loop: boolean): void {
    this.options.loop = loop;
    if (this.pathFollowingComponent) {
      this.pathFollowingComponent.setLoop(loop);
    }
  }

  /**
   * 获取当前状态
   */
  public getState(): PathFollowingState | null {
    return this.pathFollowingComponent?.getState() || null;
  }

  /**
   * 获取当前进度
   */
  public getProgress(): number {
    return this.pathFollowingComponent?.getProgress() || 0;
  }

  /**
   * 更新组件
   * @param deltaTime 时间增量
   */
  public update(deltaTime: number): void {
    if (!this.pathFollowingComponent) return;

    // 更新朝向插值
    if (this.options.enableLookAtInterpolation && this.targetLookAt && this.currentLookAt) {
      this.updateLookAtInterpolation(deltaTime);
    }

    // 更新动画过渡
    if (this.options.enableAnimationTransition && this.isTransitioning) {
      this.updateAnimationTransition(deltaTime);
    }
  }

  /**
   * 处理路径开始事件
   */
  private handlePathStarted(data: PathEventData): void {
    this.logger.info('路径跟随开始', data);
    
    if (this.avatarComponent) {
      // 设置数字人为活动状态（假设有 activate 方法）
      if (typeof (this.avatarComponent as any).activate === 'function') {
        (this.avatarComponent as any).activate();
      }
    }

    this.eventEmitter.emit('pathStarted', data);
  }

  /**
   * 处理路径完成事件
   */
  private handlePathCompleted(data: PathEventData): void {
    this.logger.info('路径跟随完成', data);
    
    if (this.avatarComponent) {
      // 播放空闲动画
      this.playAnimation('idle');
    }

    this.eventEmitter.emit('pathCompleted', data);
  }

  /**
   * 处理到达路径点事件
   */
  private handleWaypointReached(data: PathEventData): void {
    if (!this.options.path || data.currentWaypointIndex === undefined) return;

    const point = this.options.path.getPoint(data.currentWaypointIndex);
    if (!point) return;

    this.logger.debug('到达路径点', { pointIndex: data.currentWaypointIndex, point: point.toString() });

    // 更新动画
    if (point.animation !== this.currentAnimation) {
      this.playAnimation(point.animation);
    }

    // 更新朝向
    if (point.lookAt && this.options.enableLookAtInterpolation) {
      this.setLookAtTarget(point.lookAt);
    }

    this.eventEmitter.emit('waypointReached', { ...data, point });
  }

  /**
   * 处理触发器激活事件
   */
  private handleTriggerActivated(data: PathEventData): void {
    if (!this.options.enablePathEvents) return;

    const { triggerData } = data;
    if (!triggerData || !triggerData.trigger) return;

    const trigger = triggerData.trigger;
    this.logger.info('路径触发器激活', { type: trigger.type, data: trigger.data });

    // 根据触发器类型执行相应操作
    switch (trigger.type) {
      case 'animation':
        this.handleAnimationTrigger(trigger.data);
        break;
      
      case 'dialogue':
        this.handleDialogueTrigger(trigger.data);
        break;
      
      case 'sound':
        this.handleSoundTrigger(trigger.data);
        break;
      
      case 'event':
        this.handleEventTrigger(trigger.data);
        break;
      
      default:
        this.logger.warn('未知触发器类型', trigger.type);
    }

    this.eventEmitter.emit('triggerActivated', data);
  }

  /**
   * 处理路径暂停事件
   */
  private handlePathPaused(data: PathEventData): void {
    if (this.avatarComponent) {
      // 暂停当前动画或播放空闲动画
      this.playAnimation('idle');
    }

    this.eventEmitter.emit('pathPaused', data);
  }

  /**
   * 处理路径恢复事件
   */
  private handlePathResumed(data: PathEventData): void {
    if (this.avatarComponent && this.currentAnimation) {
      // 恢复当前动画
      this.playAnimation(this.currentAnimation);
    }

    this.eventEmitter.emit('pathResumed', data);
  }

  /**
   * 播放动画
   * @param animationName 动画名称
   */
  private playAnimation(animationName: string): void {
    if (!this.avatarComponent || animationName === this.currentAnimation) return;

    this.previousAnimation = this.currentAnimation;
    this.currentAnimation = animationName;

    if (this.options.enableAnimationTransition && this.previousAnimation) {
      // 启动动画过渡
      this.isTransitioning = true;
      this.animationTransitionT = 0;
    }

    // 播放新动画
    if (typeof (this.avatarComponent as any).playAnimation === 'function') {
      (this.avatarComponent as any).playAnimation(animationName);
    }
    
    this.logger.debug('播放动画', { animation: animationName, entity: this.entity?.id });
  }

  /**
   * 设置朝向目标
   * @param target 朝向目标
   */
  private setLookAtTarget(target: THREE.Vector3): void {
    this.targetLookAt = target.clone();
    this.lookAtInterpolationT = 0;

    if (!this.currentLookAt) {
      this.currentLookAt = this.entity?.getTransform().getPosition().clone() || new THREE.Vector3();
    }
  }

  /**
   * 更新朝向插值
   * @param deltaTime 时间增量
   */
  private updateLookAtInterpolation(deltaTime: number): void {
    if (!this.targetLookAt || !this.currentLookAt) return;

    this.lookAtInterpolationT += deltaTime * this.options.lookAtInterpolationSpeed!;
    this.lookAtInterpolationT = Math.min(this.lookAtInterpolationT, 1);

    // 插值计算朝向
    const currentPos = this.entity?.getTransform().getPosition() || new THREE.Vector3();
    const targetDirection = this.targetLookAt.clone().sub(currentPos).normalize();
    const currentDirection = this.currentLookAt.clone().sub(currentPos).normalize();
    
    const interpolatedDirection = currentDirection.lerp(targetDirection, this.lookAtInterpolationT);
    const targetRotation = new THREE.Quaternion().setFromUnitVectors(
      new THREE.Vector3(0, 0, 1),
      interpolatedDirection
    );

    // 应用旋转
    this.entity?.getTransform().setRotationQuaternion(targetRotation);

    // 完成插值
    if (this.lookAtInterpolationT >= 1) {
      this.currentLookAt = this.targetLookAt.clone();
      this.targetLookAt = null;
    }
  }

  /**
   * 更新动画过渡
   * @param deltaTime 时间增量
   */
  private updateAnimationTransition(deltaTime: number): void {
    this.animationTransitionT += deltaTime / this.options.animationTransitionTime!;
    this.animationTransitionT = Math.min(this.animationTransitionT, 1);

    // 完成过渡
    if (this.animationTransitionT >= 1) {
      this.isTransitioning = false;
      this.animationTransitionT = 0;
    }
  }

  /**
   * 处理动画触发器
   */
  private handleAnimationTrigger(data: any): void {
    if (data.animationName) {
      this.playAnimation(data.animationName);
    }
  }

  /**
   * 处理对话触发器
   */
  private handleDialogueTrigger(data: any): void {
    if (this.avatarComponent && data.text) {
      // 触发数字人说话
      if (typeof (this.avatarComponent as any).speak === 'function') {
        (this.avatarComponent as any).speak(data.text);
      }
    }
  }

  /**
   * 处理声音触发器
   */
  private handleSoundTrigger(data: any): void {
    // 播放声音（需要音频系统支持）
    this.logger.info('播放声音', data);
  }

  /**
   * 处理事件触发器
   */
  private handleEventTrigger(data: any): void {
    // 发射自定义事件
    this.eventEmitter.emit(data.eventName || 'customEvent', data.eventData);
  }

  /**
   * 监听事件
   * @param event 事件名称
   * @param callback 回调函数
   */
  public addEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.on(event, callback);
  }

  /**
   * 移除事件监听
   * @param event 事件名称
   * @param callback 回调函数
   */
  public removeEventListener(event: string, callback: (data: any) => void): void {
    this.eventEmitter.off(event, callback);
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    this.stopFollowing();

    if (this.pathFollowingComponent && this.entity) {
      this.entity.removeComponent(PathFollowingComponent.TYPE);
    }

    this.eventEmitter.removeAllListeners();

    // 清理引用
    this.pathFollowingComponent = null;
    this.avatarComponent = null;
    this.currentLookAt = null;
    this.targetLookAt = null;
  }
}
