version: '3.8'

services:
  # ================================
  # 基础设施层
  # ================================
  
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: dl-engine-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_ALLOW_EMPTY_PASSWORD: 'no'
      MYSQL_DATABASE: ir_engine
    ports:
      - '3306:3306'
    volumes:
      - mysql_data:/var/lib/mysql
      - ./server/shared/init-scripts:/docker-entrypoint-initdb.d
      - ./config/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost', '-u', 'root', '-p${MYSQL_ROOT_PASSWORD}']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: dl-engine-redis
    restart: always
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 2gb --maxmemory-policy allkeys-lru
    ports:
      - '6379:6379'
    volumes:
      - redis_data:/data
      - ./config/redis/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'redis-cli', '--no-auth-warning', '-a', '${REDIS_PASSWORD}', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Elasticsearch向量数据库
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    container_name: dl-engine-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms2g -Xmx2g"
      - cluster.name=dl-engine-es
      - node.name=dl-engine-es-node
    ports:
      - "9200:9200"
      - "9300:9300"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - dl-engine-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:9200/_cluster/health']
      interval: 30s
      timeout: 10s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: dl-engine-minio
    restart: always
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-admin}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD}
    ports:
      - "9000:9000"
      - "9001:9001"
    volumes:
      - minio_data:/data
    networks:
      - dl-engine-network
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # ================================
  # 核心微服务层
  # ================================

  # 服务注册中心
  service-registry:
    build:
      context: ./server/service-registry
      dockerfile: Dockerfile
    container_name: dl-engine-service-registry
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=ir_engine_registry
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - SERVICE_REGISTRY_HTTP_PORT=4010
    ports:
      - '3010:3010'
      - '4010:4010'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4010/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M

  # API网关
  api-gateway:
    build:
      context: ./server/api-gateway
      dockerfile: Dockerfile
    container_name: dl-engine-api-gateway
    restart: always
    depends_on:
      service-registry:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3000
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=1d
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=1000
    ports:
      - '3000:3000'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # 用户服务
  user-service:
    build:
      context: ./server/user-service
      dockerfile: Dockerfile
    container_name: dl-engine-user-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=ir_engine_users
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=1d
      - USER_SERVICE_HOST=user-service
      - USER_SERVICE_PORT=3001
      - USER_SERVICE_HTTP_PORT=4001
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
    ports:
      - '3001:3001'
      - '4001:4001'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4001/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # 项目服务
  project-service:
    build:
      context: ./server/project-service
      dockerfile: Dockerfile
    container_name: dl-engine-project-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=ir_engine_projects
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - PROJECT_SERVICE_HOST=project-service
      - PROJECT_SERVICE_PORT=3002
      - PROJECT_SERVICE_HTTP_PORT=4002
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
    ports:
      - '3002:3002'
      - '4002:4002'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4002/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # 资产服务
  asset-service:
    build:
      context: ./server/asset-service
      dockerfile: Dockerfile
    container_name: dl-engine-asset-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=ir_engine_assets
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - MINIO_ENDPOINT=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=${MINIO_ROOT_USER:-admin}
      - MINIO_SECRET_KEY=${MINIO_ROOT_PASSWORD}
      - ASSET_SERVICE_HOST=asset-service
      - ASSET_SERVICE_PORT=3003
      - ASSET_SERVICE_HTTP_PORT=4003
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - JWT_SECRET=${JWT_SECRET}
      - MAX_FILE_SIZE=104857600
    ports:
      - '3003:3003'
      - '4003:4003'
    volumes:
      - asset_uploads:/app/uploads
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4003/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # 渲染服务
  render-service:
    build:
      context: ./server/render-service
      dockerfile: Dockerfile
    container_name: dl-engine-render-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=ir_engine_render
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - RENDER_SERVICE_HOST=render-service
      - RENDER_SERVICE_PORT=3004
      - RENDER_SERVICE_HTTP_PORT=4004
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - JWT_SECRET=${JWT_SECRET}
      - RENDER_QUEUE_CONCURRENCY=4
      - RENDER_TIMEOUT=300000
    ports:
      - '3004:3004'
      - '4004:4004'
    volumes:
      - render_outputs:/app/renders
      - render_temp:/app/temp
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4004/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # 协作服务（主实例）
  collaboration-service-1:
    build:
      context: ./server/collaboration-service
      dockerfile: Dockerfile
    container_name: dl-engine-collaboration-service-1
    restart: always
    depends_on:
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3005
      - INSTANCE_ID=collaboration-1
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - ENABLE_COMPRESSION=true
      - COMPRESSION_LEVEL=6
      - MAX_BATCH_SIZE=50
      - MAX_BATCH_WAIT_TIME=50
      - MAX_CONCURRENT_USERS=100
    ports:
      - '3005:3005'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'wget', '-qO-', 'http://localhost:3005/health']
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # 协作服务（副本实例）
  collaboration-service-2:
    build:
      context: ./server/collaboration-service
      dockerfile: Dockerfile
    container_name: dl-engine-collaboration-service-2
    restart: always
    depends_on:
      redis:
        condition: service_healthy
      service-registry:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3006
      - INSTANCE_ID=collaboration-2
      - JWT_SECRET=${JWT_SECRET}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - SERVICE_REGISTRY_HOST=service-registry
      - SERVICE_REGISTRY_PORT=3010
      - ENABLE_COMPRESSION=true
      - COMPRESSION_LEVEL=6
      - MAX_BATCH_SIZE=50
      - MAX_BATCH_WAIT_TIME=50
      - MAX_CONCURRENT_USERS=100
    ports:
      - '3006:3006'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'wget', '-qO-', 'http://localhost:3006/health']
      interval: 10s
      timeout: 5s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # 协作服务负载均衡
  collaboration-load-balancer:
    image: nginx:alpine
    container_name: dl-engine-collaboration-lb
    restart: always
    depends_on:
      collaboration-service-1:
        condition: service_healthy
      collaboration-service-2:
        condition: service_healthy
    volumes:
      - ./server/collaboration-service/nginx.conf:/etc/nginx/conf.d/default.conf
    ports:
      - '3007:80'
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'wget', '-qO-', 'http://localhost:80/health']
      interval: 10s
      timeout: 5s
      retries: 3

  # ================================
  # AI智能服务层
  # ================================

  # 知识库服务
  knowledge-base-service:
    build:
      context: ./server/knowledge-base-service
      dockerfile: Dockerfile
    container_name: dl-engine-knowledge-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=4011
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=ir_engine_knowledge
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - EMBEDDING_MODEL=text-embedding-ada-002
      - CHUNK_SIZE=1000
      - CHUNK_OVERLAP=200
    ports:
      - "4011:4011"
    volumes:
      - ./uploads/knowledge:/app/uploads
      - ./temp/knowledge:/app/temp
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4011/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # RAG对话服务
  rag-dialogue-service:
    build:
      context: ./server/rag-dialogue-service
      dockerfile: Dockerfile
    container_name: dl-engine-rag-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      knowledge-base-service:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=4012
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=ir_engine_rag
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - KNOWLEDGE_SERVICE_URL=http://knowledge-base-service:4011
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - AZURE_OPENAI_ENDPOINT=${AZURE_OPENAI_ENDPOINT}
      - AZURE_OPENAI_API_KEY=${AZURE_OPENAI_API_KEY}
      - CHAT_MODEL=gpt-3.5-turbo
      - MAX_TOKENS=2000
      - TEMPERATURE=0.7
    ports:
      - "4012:4012"
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4012/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # 数字人服务
  avatar-service:
    build:
      context: ./server/avatar-service
      dockerfile: Dockerfile
    container_name: dl-engine-avatar-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      rag-dialogue-service:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=4013
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=ir_engine_avatars
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - RAG_SERVICE_URL=http://rag-dialogue-service:4012
      - AVATAR_MODELS_PATH=/app/assets/models
      - ANIMATION_CACHE_SIZE=1000
    ports:
      - "4013:4013"
    volumes:
      - ./uploads/avatars:/app/uploads
      - ./assets/models:/app/assets/models
      - ./assets/textures:/app/assets/textures
      - avatar_cache:/app/cache
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4013/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # 语音服务
  voice-service:
    build:
      context: ./server/voice-service
      dockerfile: Dockerfile
    container_name: dl-engine-voice-service
    restart: always
    depends_on:
      redis:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=4014
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - AZURE_SPEECH_KEY=${AZURE_SPEECH_KEY}
      - AZURE_SPEECH_REGION=${AZURE_SPEECH_REGION}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_CLOUD_KEY_FILE=/app/config/google-cloud-key.json
      - BAIDU_APP_ID=${BAIDU_APP_ID}
      - BAIDU_API_KEY=${BAIDU_API_KEY}
      - BAIDU_SECRET_KEY=${BAIDU_SECRET_KEY}
      - VOICE_CACHE_TTL=3600
      - MAX_AUDIO_DURATION=300
    ports:
      - "4014:4014"
    volumes:
      - ./uploads/voice:/app/uploads
      - ./temp/voice:/app/temp
      - ./config/google-cloud-key.json:/app/config/google-cloud-key.json:ro
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:4014/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.5'
          memory: 3G
        reservations:
          cpus: '0.75'
          memory: 1.5G

  # 推荐服务
  recommendation-service:
    build:
      context: ./server/recommendation-service
      dockerfile: Dockerfile
    container_name: dl-engine-recommendation-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3009
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=ir_engine_recommendations
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - MODEL_STORAGE_PATH=/app/models
      - RECOMMENDATION_CACHE_TTL=1800
      - MAX_RECOMMENDATIONS=50
    ports:
      - "3009:3009"
    volumes:
      - ./models/recommendation:/app/models
      - recommendation_cache:/app/cache
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3009/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # AI模型服务
  ai-model-service:
    build:
      context: ./server/ai-model-service
      dockerfile: Dockerfile
    container_name: dl-engine-ai-model-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3008
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=ir_engine_ai_models
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MODEL_STORAGE_PATH=/app/models
      - GPU_ENABLED=false
      - MODEL_CACHE_SIZE=10
    ports:
      - "3008:3008"
    volumes:
      - ./models/ai:/app/models
      - ai_model_cache:/app/cache
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3008/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 6G
        reservations:
          cpus: '1.0'
          memory: 3G

  # ================================
  # 区块链服务层
  # ================================

  # 区块链服务
  blockchain-service:
    build:
      context: ./server/blockchain-service
      dockerfile: Dockerfile
    container_name: dl-engine-blockchain-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3006
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=ir_engine_blockchain
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - ETHEREUM_RPC_URL=${ETHEREUM_RPC_URL}
      - POLYGON_RPC_URL=${POLYGON_RPC_URL}
      - PRIVATE_KEY=${BLOCKCHAIN_PRIVATE_KEY}
      - CONTRACT_ADDRESS=${NFT_CONTRACT_ADDRESS}
      - MARKETPLACE_CONTRACT_ADDRESS=${MARKETPLACE_CONTRACT_ADDRESS}
      - GAS_LIMIT=500000
      - GAS_PRICE=20000000000
    ports:
      - "3006:3006"
    volumes:
      - ./contracts:/app/contracts
      - blockchain_data:/app/data
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3006/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # ================================
  # 边缘计算层
  # ================================

  # 边缘游戏服务器
  edge-game-server:
    build:
      context: ./server/edge-game-server
      dockerfile: Dockerfile
    container_name: dl-engine-edge-game-server
    restart: always
    depends_on:
      redis:
        condition: service_healthy
      api-gateway:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=8080
      - GAME_SERVER_PORT=3030
      - WEBRTC_PORT=10000
      - EDGE_NODE_ID=edge-main-${HOSTNAME:-node}
      - EDGE_REGION=${EDGE_REGION:-main}
      - CENTRAL_HUB_URL=http://api-gateway:3000
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - MAX_USERS_PER_EDGE=${MAX_USERS_PER_EDGE:-100}
      - EDGE_CACHE_MAX_SIZE=10000
      - EDGE_CACHE_TTL=3600
      - SYNC_INTERVAL=30000
    ports:
      - "8080:8080"
      - "3030:3030"
      - "10000:10000/udp"
    volumes:
      - edge_cache:/app/cache
      - edge_logs:/app/logs
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:8080/api/edge/health']
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # 信令服务
  signaling-service:
    build:
      context: ./server/signaling-service
      dockerfile: Dockerfile
    container_name: dl-engine-signaling-service
    restart: always
    depends_on:
      redis:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3020
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - STUN_SERVERS=stun:stun.l.google.com:19302,stun:stun1.l.google.com:19302
      - TURN_SERVER=${TURN_SERVER_URL}
      - TURN_USERNAME=${TURN_USERNAME}
      - TURN_PASSWORD=${TURN_PASSWORD}
      - MAX_ROOM_SIZE=50
      - ROOM_TIMEOUT=3600000
    ports:
      - "3020:3020"
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3020/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # ================================
  # 前端应用层
  # ================================

  # 编辑器前端
  editor:
    build:
      context: ./editor
      dockerfile: Dockerfile
    container_name: dl-engine-editor
    restart: always
    depends_on:
      api-gateway:
        condition: service_healthy
      collaboration-load-balancer:
        condition: service_healthy
      voice-service:
        condition: service_healthy
    environment:
      - REACT_APP_API_URL=http://localhost:3000/api
      - REACT_APP_COLLABORATION_SERVER_URL=ws://localhost:3007
      - REACT_APP_VOICE_SERVICE_URL=ws://localhost:4014
      - REACT_APP_KNOWLEDGE_SERVICE_URL=http://localhost:4011
      - REACT_APP_RAG_SERVICE_URL=http://localhost:4012
      - REACT_APP_AVATAR_SERVICE_URL=http://localhost:4013
      - REACT_APP_BLOCKCHAIN_SERVICE_URL=http://localhost:3006
      - REACT_APP_EDGE_SERVER_URL=http://localhost:8080
      - REACT_APP_SIGNALING_SERVER_URL=ws://localhost:3020
    ports:
      - '80:80'
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M

  # ================================
  # 监控运维层
  # ================================

  # 监控服务
  monitoring-service:
    build:
      context: ./server/monitoring-service
      dockerfile: Dockerfile
    container_name: dl-engine-monitoring-service
    restart: always
    depends_on:
      mysql:
        condition: service_healthy
      elasticsearch:
        condition: service_healthy
    environment:
      - NODE_ENV=production
      - PORT=3100
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_USERNAME=root
      - DB_PASSWORD=${MYSQL_ROOT_PASSWORD}
      - DB_DATABASE=ir_engine_monitoring
      - ELASTICSEARCH_URL=http://elasticsearch:9200
      - ALERT_EMAIL=${ALERT_EMAIL}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_PORT=${SMTP_PORT}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - METRICS_RETENTION_DAYS=30
      - LOG_RETENTION_DAYS=7
    ports:
      - "3100:3100"
    volumes:
      - ./logs:/app/logs
      - monitoring_data:/app/data
    networks:
      - dl-engine-network
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3100/health']
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: dl-engine-nginx
    restart: always
    depends_on:
      - editor
      - api-gateway
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./ssl:/etc/nginx/ssl
    ports:
      - "443:443"
      - "8443:8443"
    networks:
      - dl-engine-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M

# ================================
# 网络配置
# ================================
networks:
  dl-engine-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
    driver_opts:
      com.docker.network.bridge.name: dl-engine-br0

# ================================
# 数据卷配置
# ================================
volumes:
  # 基础设施数据卷
  mysql_data:
    driver: local
  redis_data:
    driver: local
  elasticsearch_data:
    driver: local
  minio_data:
    driver: local

  # 应用数据卷
  asset_uploads:
    driver: local
  render_outputs:
    driver: local
  render_temp:
    driver: local
  avatar_cache:
    driver: local
  recommendation_cache:
    driver: local
  ai_model_cache:
    driver: local
  blockchain_data:
    driver: local
  edge_cache:
    driver: local
  edge_logs:
    driver: local
  monitoring_data:
    driver: local

# ================================
# 扩展配置（可选）
# ================================

# 开发环境覆盖配置
# 使用: docker-compose -f docker-compose.complete.yml -f docker-compose.dev.yml up
x-development-overrides: &dev-overrides
  environment:
    - NODE_ENV=development
    - LOG_LEVEL=debug
  volumes:
    - ./:/app/src
  command: npm run start:dev

# 生产环境资源限制
x-production-resources: &prod-resources
  deploy:
    resources:
      limits:
        cpus: '2.0'
        memory: 4G
      reservations:
        cpus: '1.0'
        memory: 2G
    restart_policy:
      condition: on-failure
      delay: 5s
      max_attempts: 3
      window: 120s

# 健康检查配置
x-health-check: &health-check
  healthcheck:
    interval: 30s
    timeout: 10s
    retries: 3
    start_period: 60s

# 日志配置
x-logging: &logging
  logging:
    driver: "json-file"
    options:
      max-size: "100m"
      max-file: "3"
      labels: "service,environment"
